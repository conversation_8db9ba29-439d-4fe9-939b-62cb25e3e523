<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use app\common\model\FilmSession as FilmSessionModel;
use app\common\model\FilmSubscribe;
use app\common\model\FilmBlackList;
use app\common\model\UserLinkman;
use app\common\model\WebUser;
use think\Request;
use think\Cache;
use think\Validate;
use think\exception\ValidateException;
use think\Db;
use fast\Random;

/**
 * 影片场次控制器
 * @ApiWeigh (98)
 */
class FilmSession extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

   

  

    /**
     * 个人中心影片信息 - 对应Java版本personalCenterFilm方法
     *
     * @ApiTitle    (个人中心影片信息)
     * @ApiSummary  (获取用户个人中心的影片预订信息列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/personalCenterFilm)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function personalCenterFilm()
    {
        $userId = $this->getUserId();

        // 对应Java版本的startPage()分页处理
        $pageNum = $this->request->param('pageNum', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 10, 'intval');

        // 1. 查询用户的批次号列表 
        $batchNumbers = $this->selectBatchNumberByUserId($userId);
        $total = count($batchNumbers);

        // 2. 查询影片信息
        $personalCenterFilms = $this->getPersonalCenterFilmData($userId, $pageNum, $pageSize);

        // 3. 格式化返回数据
        $formattedData = $this->formatPersonalCenterFilmData($personalCenterFilms);

        $result = [
            'total' => $total,
            'rows' => $formattedData
        ];

        return $this->success('操作成功', $result);
    }

    /**
     * 查询用户批次号列表 - 对应Java版本selectBatchNumberByUserId
     * @param int $userId 用户ID
     * @return array 批次号列表
     */
    private function selectBatchNumberByUserId($userId)
    {
        return FilmSubscribe::where('user_id', $userId)
            ->where('del_flag', '0')
            ->group('batch_number,create_time')
            ->order('create_time desc')
            ->column('batch_number');
    }

    /**
     * 获取个人中心影片数据 - 对应Java版本personalCenterFilm的SQL
     * @param int $userId 用户ID
     * @param int $pageNum 页码
     * @param int $pageSize 页大小
     * @return array 影片数据
     */
    private function getPersonalCenterFilmData($userId, $pageNum, $pageSize)
    {
        // 完全对应Java版本的SQL结构，使用子查询而不是预先查询批次号
        $offset = ($pageNum - 1) * $pageSize;

        return \think\Db::query("
            SELECT DISTINCT
                f.film_type as filmType,
                f.film_name as filmName,
                f.film_time as filmTime,
                f.file_introduce as fileIntroduce,
                f.film_cover as filmCover,
                f.id as filmId,

                fs.id as filmSeeionId,
                fs.film_start_time as filmStartTime,
                fs.film_end_time as filmEndTime,
                fs.film_arranged_date as filmArrangedDate,
                fs.year,
                fs.week,
                fs.inventory_votes as inventoryVotes,
                fs.film_poll as filmPoll,

                fsb.subscribe_state as subscribeState,
                fsb.subscribe_type as subscribeType,
                fsb.batch_number as batchNumber,
                fsb.is_surplus as isSurplus,
                fsb.is_ticket as isTicket
            FROM film f, film_session fs, film_subscribe fsb
            WHERE f.id=fs.film_id AND fs.id=fsb.film_seeion_id
            AND fsb.batch_number IN (
                SELECT fs.batch_number FROM film_subscribe fs
                WHERE fs.user_id={$userId} AND fs.del_flag ='0'
                GROUP BY fs.batch_number,fs.create_time
                ORDER BY fs.create_time desc
            )
            ORDER BY fs.film_start_time desc
            LIMIT {$offset}, {$pageSize}
        ");
    }

    /**
     * 格式化个人中心影片数据 - 对应Java版本的@JsonFormat注解
     * @param array $data 原始数据
     * @return array 格式化后的数据
     */
    private function formatPersonalCenterFilmData($data)
    {
        foreach ($data as &$item) {
            // 对应Java版本@JsonFormat(pattern="yyyy/MM/dd HH:mm:ss", timezone="GMT+8")
            if (isset($item['filmStartTime'])) {
                $item['filmStartTime'] = date('Y/m/d H:i:s', strtotime($item['filmStartTime']));
            }
            if (isset($item['filmEndTime'])) {
                $item['filmEndTime'] = date('Y/m/d H:i:s', strtotime($item['filmEndTime']));
            }
            // 对应Java版本@JsonFormat(pattern="yyyy/MM/dd", timezone="GMT+8")
            if (isset($item['filmArrangedDate'])) {
                $item['filmArrangedDate'] = date('Y/m/d', strtotime($item['filmArrangedDate']));
            }
        }

        return $data;
    }
    

    /**
     * 获取影片预约人员信息
     * 
     * @ApiTitle    (获取影片预约人员信息)
     * @ApiSummary  (根据批次号获取影片预约的观影人员信息列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/getFilmSubscribePeoples)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     */
    public function getFilmSubscribePeoples()
    {
        $batchNumber = $this->request->param('batchNumber');
        
        // 参数验证
        if (empty($batchNumber)) {
            $this->error('批次号不能为空');
        }
        
      
        // 查询观影人员信息
        $peoples = FilmSubscribe::alias('c')
            ->join(['user_linkman' => 'l'], 'l.id = c.user_linkman_id AND l.del_flag = "0"', 'INNER')
            ->field([
                'c.user_linkman_id as linkId',
                'l.linkman_name as linkmanName', 
                'l.linkman_phone as linkmanPhone',
                'l.linkman_age as linkmanAge',
                'l.linkman_certificate as linkmanCertificate',
                '1 as linkCheck'  // 固定返回true，
            ])
            ->where('c.batch_number', $batchNumber)
            ->where('c.subscribe_state', '<>', '3')  // 排除已取消的预约
            ->where('c.del_flag', '0')
            ->select();
        // 转换linkCheck为布尔值
        foreach ($peoples as &$people) {
            $people['linkCheck'] = true;
        }
        
        $this->success('操作成功', $peoples);
      
    }

    /**
     * 取消影片预约
     * 
     * @ApiTitle    (取消影片预约)
     * @ApiSummary  (取消用户的影片预约，支持取消整个批次或指定人员)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/cancelFilmSession)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     * @ApiParams   (name="filmSessionId", type="integer", required=true, description="影片场次ID")
     * @ApiParams   (name="vote", type="integer", required=true, description="票数/人数")
     * @ApiParams   (name="peopleIds", type="array", required=false, description="人员ID列表，指定要取消的具体人员")
     */
    public function cancelFilmSession()
    {
        $batchNumber = $this->request->param('batchNumber');
        $filmSessionId = $this->request->param('filmSessionId');
        $vote = $this->request->param('vote');
        $peopleIds = $this->request->param('peopleIds', []);
        
        // 参数验证
        if (empty($batchNumber)) {
            $this->error('批次号不能为空');
        }
        if (empty($filmSessionId)) {
            $this->error('影片场次ID不能为空');
        }
        if (empty($vote) || $vote == 0) {
            $this->error('票数有误');
        }
        
    
        Db::startTrans();
        
        $cancelNum = $vote;
        if (!empty($peopleIds) && $vote > count($peopleIds)) {
            $cancelNum = count($peopleIds);
        }
        
        // 获取影片场次信息
        $filmSession = FilmSessionModel::where('id', $filmSessionId)->find();
        if (!$filmSession) {
            $this->error('影片场次不存在');
        }
        
        // 检查时间限制（开场前10分钟不允许取消）
        $filmStartTime = strtotime($filmSession['film_start_time']);
        $checkTime = $filmStartTime - (10 * 60); // 开场前10分钟
        if (time() >= $checkTime) {
            $this->error('开场时间前10分钟不允许取消');
        }
        
        // 检查是否已有检票记录
        $hasTicket = FilmSubscribe::where('batch_number', $batchNumber)
            ->where('is_ticket', '1')
            ->count();
        if ($hasTicket > 0) {
            $this->error('已有检票,不能取消');
        }
        
        // 更新库存票数
        $currentInventory = $filmSession['inventory_votes'];
        $newInventory = $currentInventory + $cancelNum;
        FilmSessionModel::where('id', $filmSessionId)
            ->update(['inventory_votes' => $newInventory]);
        
        // 更新预约类型
        $newSubscribeType = $vote - $cancelNum;
        FilmSubscribe::where('batch_number', $batchNumber)
            ->update(['subscribe_type' => $newSubscribeType]);
        
        // 取消预约记录
        $affectedRows = 0;
        if (!empty($peopleIds)) {
            // 取消指定人员的预约
            foreach ($peopleIds as $linkId) {
                $result = FilmSubscribe::where('batch_number', $batchNumber)
                    ->where('user_linkman_id', $linkId)
                    ->delete();
                if ($result > 0) {
                    $affectedRows += $result;
                } else {
                    Db::rollback();
                    $this->error('批次号有误');
                }
            }
        } else {
            // 取消整个批次的预约
            $affectedRows = FilmSubscribe::where('batch_number', $batchNumber)->delete();
        }
        
        Db::commit();
        $this->success('取消成功', $affectedRows);
      
    }

    /**
     * 通过场次ID获取个人中心影片信息
     * @ApiTitle    (通过场次ID获取个人中心影片信息)
     * @ApiSummary  (根据影片场次ID获取详细的影片和场次信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/personalCenterFilmByFilmSessionId)
     * @ApiParams   (name="filmSessionId", type="integer", required=true, description="影片场次ID")
     */
    public function personalCenterFilmByFilmSessionId()
    {
        // 获取影片场次ID参数
        $filmSessionId = $this->request->param('filmSessionId', 0, 'intval');

        if (!$filmSessionId) {
            return $this->error('影片场次ID不能为空');
        }

      
        // 查询影片场次详细信息
        $filmInfo = $this->getFilmSessionDetail($filmSessionId);

        if (!$filmInfo) {
            return $this->error('未找到对应的影片场次信息');
        }

        return $this->success('获取成功', $filmInfo);

    }

    /**
     * 获取影片场次详细信息
     * @param int $filmSessionId 影片场次ID
     * @return array|null
     */
    private function getFilmSessionDetail($filmSessionId)
    {
        // 查询影片和场次信息
        $filmInfo = \app\common\model\FilmSession::alias('fs')
            ->join(['film' => 'f'], 'f.id = fs.film_id')
            ->field([
                'f.film_type as filmType',
                'f.film_name as filmName',
                'f.file_introduce as fileIntroduce',
                'f.film_cover as filmCover',
                'f.id as filmId',
                'fs.id as filmSeeionId',
                'fs.film_start_time as filmStartTime',
                'fs.film_end_time as filmEndTime',
                'fs.film_arranged_date as filmArrangedDate',
                'fs.year',
                'fs.week',
                'fs.inventory_votes as inventoryVotes',
                'fs.film_poll as filmPoll'
            ])
            ->where('fs.id', $filmSessionId)
            ->find();

        if (!$filmInfo) {
            return null;
        }

        // 格式化时间字段
        if ($filmInfo['filmStartTime']) {
            $filmInfo['filmStartTime'] = date('Y/m/d H:i:s', strtotime($filmInfo['filmStartTime']));
        }

        if ($filmInfo['filmEndTime']) {
            $filmInfo['filmEndTime'] = date('Y/m/d H:i:s', strtotime($filmInfo['filmEndTime']));
        }

        if ($filmInfo['filmArrangedDate']) {
            $filmInfo['filmArrangedDate'] = date('Y/m/d', strtotime($filmInfo['filmArrangedDate']));
        }

        // 构造影片时间字段 (开始时间-结束时间)
        if ($filmInfo['filmStartTime'] && $filmInfo['filmEndTime']) {
            $startTime = date('H:i:s', strtotime($filmInfo['filmStartTime']));
            $endTime = date('H:i:s', strtotime($filmInfo['filmEndTime']));
            $filmInfo['filmTime'] = $startTime . '-' . $endTime;
        }

        // 设置默认值
        $filmInfo['subscribeState'] = '';
        $filmInfo['subscribeType'] = '';
        $filmInfo['batchNumber'] = '';
        $filmInfo['isSurplus'] = '';
        $filmInfo['isTicket'] = '';

        return $filmInfo;
    }

    /**
     * 检查批次号的预约状态
     * @ApiTitle    (检查批次号的预约状态)
     * @ApiSummary  (通过批次号查询判断用户是否签到)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/isCheck)
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     */
    public function isCheck()
    {
        // 获取批次号参数
        $batchNumber = $this->request->param('batchNumber', '');

        // 参数验证
        if (empty($batchNumber)) {
            return $this->error('批次号不能为空');
        }


        // 查询批次号的预约状态
        $subscribeState = $this->getSubscribeStateByBatchNumber($batchNumber);

        return $this->success('操作成功', $subscribeState);

    }

    /**
     * 通过批次号进行签到 - 对应Java版本check接口
     * @ApiTitle    (通过批次号进行签到)
     * @ApiSummary  (通过批次号将预约状态从1更新为4，表示已签到)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/check)
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     */
    public function check()
    {
        // 获取批次号参数
        $batchNumber = $this->request->param('batchNumber', '');

        // 参数验证
        if (empty($batchNumber)) {
            return $this->error('批次号不能为空');
        }

        // 执行签到操作 
        $affectedRows = $this->checkByBatchNumber($batchNumber);
        
        if ($affectedRows > 0) {
            return $this->success('操作成功');
        } else {
            return $this->error('操作失败');
        }
    }

    /**
     * 获取二维码信息 - 对应Java版本qrCodeInfo接口
     * @ApiTitle    (获取二维码信息)
     * @ApiSummary  (根据批次号和影片类型获取二维码信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/qrCodeInfo)
     * @ApiParams   (name="batchNumber", type="string", required=true, description="批次号")
     * @ApiParams   (name="filmType", type="string", required=true, description="影片类型")
     */
    public function qrCodeInfo()
    {
        // 获取请求参数
        $batchNumber = $this->request->param('batchNumber');
        $filmType = $this->request->param('filmType');

        // 参数验证
        if (empty($batchNumber)) {
            return $this->error('批次号不能为空');
        }
        if (empty($filmType)) {
            return $this->error('影片类型不能为空');
        }

        // 调用业务逻辑获取二维码信息
        $qrCodeList = $this->getQrCodeInfo($batchNumber, $filmType);

        return $this->success('操作成功', $qrCodeList);
    
    
    }

    /**
     * 获取二维码信息业务逻辑 - 对应Java中FilmSubscribeServiceImpl.qrCodeInfo方法
     * @param string $batchNumber 批次号
     * @param string $filmType 影片类型
     * @return array 二维码信息列表
     * @throws \Exception
     */
    private function getQrCodeInfo($batchNumber, $filmType)
    {
        // 根据批次号查询影片预约记录
        $filmSubscribes = $this->getFilmSubscribeByBatchNumber($batchNumber);

        $codeInfoList = [];
        $expireTime = time() * 1000; // 转换为毫秒时间戳
        $code = $this->getFourBitRandom(); // 生成4位随机码

        foreach ($filmSubscribes as $fs) {
            $key = "film_subscribe:" . $fs['id'];

            // 从Redis获取缓存数据
            $cachedData = \think\Cache::get($key);

            // 获取联系人信息
            $userLinkman = $this->getUserLinkmanById($fs['user_linkman_id']);
            if (!$userLinkman || $userLinkman['del_flag'] == '2') {
               $this->error('联系人不存在');
            }

            // 构建二维码内容 
            $content = [
                'id' => $fs['id'],
                'filmSeeionId' => $fs['film_seeion_id'],
                'userId' => $fs['user_id'],
                'linkId' => $fs['user_linkman_id'],
                'subscribeState' => $fs['subscribe_state'],
                'isTicket' => null, 
                'batchNumber' => $fs['batch_number'],
                'time' => $expireTime,
                'code' => $code,
                'type' => $filmType,
                'linkmanName' => $userLinkman['linkman_name'],
                'linkmanPhone' => $userLinkman['linkman_phone'],
                'linkmanAge' => $userLinkman['linkman_age'],
                'linkmanCertificate' => $userLinkman['linkman_certificate'],
                'linkmanCertificateType' => $userLinkman['linkman_certificate_type'],
                'showTime' => $this->getCurrentTime()
            ];

            // 生成加密的二维码参数
            $qrCode = "film:" . $this->encryptDecryptHex(json_encode($content, JSON_UNESCAPED_UNICODE));

            if ($cachedData === false || $cachedData === null) {
                // 缓存不存在，创建新的二维码信息
                $filmQrCodeVo = [
                    'count' => '1',
                    'time' => $expireTime,
                    'code' => $code,
                    'param' => $qrCode,
                    'tureId' => (string)$fs['id'],
                    'flag' => '1'
                ];

                // 缓存12小时
                \think\Cache::set($key, json_encode($filmQrCodeVo, JSON_UNESCAPED_UNICODE), 12 * 3600);

                // 移除tureId字段
                unset($filmQrCodeVo['tureId']);
                $codeInfoList[] = $filmQrCodeVo;
            } else {
                // 缓存存在，更新二维码信息
                $filmQrCodeVo = json_decode($cachedData, true);
                $filmQrCodeVo['time'] = $expireTime;
                $filmQrCodeVo['code'] = $code;
                $filmQrCodeVo['param'] = $qrCode;

                // 更新缓存
                \think\Cache::set($key, json_encode($filmQrCodeVo, JSON_UNESCAPED_UNICODE), 12 * 3600);

                // 移除tureId字段
                unset($filmQrCodeVo['tureId']);
                $codeInfoList[] = $filmQrCodeVo;
            }
        }

        return $codeInfoList;
    }

    /**
     * 通过批次号查询预约状态 
     * @param string $batchNumber 批次号
     * @return string
     */
    private function getSubscribeStateByBatchNumber($batchNumber)
    {
       
        $sql = "SELECT DISTINCT subscribe_state FROM film_subscribe WHERE batch_number = ?";
        $result = \think\Db::query($sql, [$batchNumber]);

        if (empty($result)) {
            return '';
        }

        // 如果只有一个状态，直接返回该状态
        if (count($result) == 1) {
            return $result[0]['subscribe_state'];
        }

        // 如果有多个不同状态，返回第一个状态
        return $result[0]['subscribe_state'];
    }

    /**
     * 个人场馆预约估算
     * @ApiTitle    (个人场馆预约估算)
     * @ApiSummary  (判断从当前时刻-未来的时间，是否有场馆预约，用于影视预约条件校验)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/subscribeEstimate)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function subscribeEstimate()
    {
        // 获取用户ID
        $userId = $this->getUserId();
        // 查询用户的场馆预约估算信息
        $venueSubscribeList = $this->getPersonalVenueSubscribeEstimate($userId);

        return $this->success('获取成功', $venueSubscribeList);
    }

    /**
     * 获取个人场馆预约估算数据
     * @param int $userId 用户ID
     * @return array
     */
    private function getPersonalVenueSubscribeEstimate($userId)
    {
        $currentTime = date('Y-m-d H:i:s');

        // 查询用户的场馆预约信息（场馆结束时间大于当前时间的预约）
        $venueSubscribeList = \app\common\model\VenueSubscribe::alias('vs')
            ->join(['venue' => 'v'], 'v.id = vs.venue_id')
            ->field([
                'v.id',
                'vs.user_id as userId',
                'vs.sign_state as signState',
                'v.venue_start_time as venueStartTime',
                'v.venue_end_time as venueEndTime'
            ])
            ->where('vs.user_id', $userId)
            ->where('vs.del_flag', '0')
            ->where('v.venue_end_time', '>', $currentTime)
            ->order('v.venue_start_time', 'asc')
            ->select();

        // 格式化时间字段
        foreach ($venueSubscribeList as &$item) {
            if ($item['venueStartTime']) {
                $item['venueStartTime'] = date('Y-m-d', strtotime($item['venueStartTime']));
            }
            if ($item['venueEndTime']) {
                $item['venueEndTime'] = date('Y-m-d', strtotime($item['venueEndTime']));
            }
        }

        return $venueSubscribeList;
    }

    /**
     * 检查用户是否在黑名单中
     * @ApiTitle    (检查用户是否在黑名单中)
     * @ApiSummary  (判断用户是否为黑名单用户，返回解锁时间或false)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/isBalckList)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function isBalckList()
    {
        // 获取用户ID
        $userId = $this->getUserId();

        // 查询用户是否在黑名单中
        $unlockTime = $this->checkUserBlackList($userId);

        if ($unlockTime === null) {
            // 用户不在黑名单中
            return $this->success('操作成功', false);
        }

        // 用户在黑名单中，返回解锁时间
        return $this->success('操作成功', $unlockTime);
    }

    /**
     * 检查用户黑名单状态
     * @param int $userId 用户ID
     * @return string|null 返回解锁时间或null
     */
    private function checkUserBlackList($userId)
    {
        // 查询用户黑名单记录
        $unlockTime = \app\common\model\FilmBlackList::where('user_id', $userId)
            ->where('del_flag', '0')
            ->where('is_start', '0')
            ->value('unlock_time');

        return $unlockTime;
    }

    /**
     * 影片预约
     * @ApiTitle    (影片预约)
     * @ApiSummary  (用户预约影片场次)
     * @ApiMethod   (POST)
     * @ApiRoute    (/web/fileSession/reservations)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="filmSeeionId", type="integer", required=true, description="影片场次ID")
     * @ApiParams   (name="subscribeType", type="string", required=true, description="预约票类型")
     * @ApiParams   (name="userLinkmanIdList", type="string", required=true, description="用户联系人ID列表，逗号分隔")
     * @ApiParams   (name="isSurplus", type="string", required=true, description="是否为余票(0/正常票 1/余票)")
     */
    public function reservations()
    {
        // 获取用户ID
        $userId = $this->getUserId();
        // 获取请求参数

          $param = $this->request->header('content-type');
        if (strpos($param, 'application/json') !== false) {
            // JSON数据处理
            $param = json_decode($this->request->getContent(), true);
            if (!$param) {
                return $this->error('JSON数据格式错误');
            }
        } else {
            // 表单数据处理
            $param = $this->request->post();
        }

       // $param = array('filmSeeionId'=>'375','subscribeType'=>'1','userLinkmanIdList'=>'115','isSurplus'=>'0');
        // 参数验证
        if (empty($param['filmSeeionId'])) {
            return $this->error('影片场次ID不能为空');
        }
        if (empty($param['subscribeType'])) {
            return $this->error('预约票类型不能为空');
        }
        if (empty($param['userLinkmanIdList'])) {
            return $this->error('参数传递错误');
        }
        if (!isset($param['isSurplus'])) {
            return $this->error('是否为余票不能为空');
        }

        $filmSeeionId = $param['filmSeeionId'];
        $subscribeType = $param['subscribeType'];
        $userLinkmanIdList = $param['userLinkmanIdList'];
        $isSurplus = $param['isSurplus'];
     
        // 执行预约逻辑
        $batchNumber = $this->doReservations($userId, $filmSeeionId, $subscribeType, $userLinkmanIdList, $isSurplus);

        return $this->success('预约成功', $batchNumber);

    }

    /**
     * 执行影片预约逻辑
     * @param int $userId 用户ID
     * @param int $filmSeeionId 影片场次ID
     * @param string $subscribeType 预约票类型
     * @param string $userLinkmanIdList 联系人ID列表
     * @param string $isSurplus 是否余票
     * @return string 批次号
     */
    private function doReservations($userId, $filmSeeionId, $subscribeType, $userLinkmanIdList, $isSurplus)
    {
        // 检查是否重复预约 - 完全对应Java版本selectByUserIdAndFilmSessionId
        $existCount = FilmSubscribe::where('film_seeion_id', $filmSeeionId)
            ->where('user_id', $userId)
            ->where('del_flag', '0')
            ->whereIn('subscribe_state', ['1', '2', '4', '5'])
            ->count();

        if ($existCount > 0) {
            return $this->error('请勿重复预约');
        }

        // 解析联系人ID列表
        $userLinkmanIds = explode(',', $userLinkmanIdList);
        $userLinkmanIds = array_filter($userLinkmanIds); // 过滤空值

        if (empty($userLinkmanIds)) {
            return $this->error('联系人ID列表不能为空');
        }

        // 生成批次号
        $batchNumber = $this->generateUuid();

        // 构建预约记录列表
        $filmSubscribeList = [];
        $currentTime = date('Y-m-d H:i:s');

        foreach ($userLinkmanIds as $linkmanId) {
            $filmSubscribeData = [
                'film_seeion_id' => $filmSeeionId,
                'user_id' => $userId,
                'user_linkman_id' => intval($linkmanId),
                'subscribe_type' => $subscribeType,
                'batch_number' => $batchNumber,
                'del_flag' => '0',
                'create_time' => $currentTime,
                'update_time' => $currentTime
            ];

            // 完全对应Java版本的预约状态设置逻辑
            if ($isSurplus === '1') {
                // 余票直接设置为已签到状态
                $filmSubscribeData['subscribe_state'] = '4';
                $filmSubscribeData['is_surplus'] = '1';
            } else {
                // 正常票，检查用户是否有未来的场馆预约
                $filmSubscribeData['is_surplus'] = '0';

                if ($this->checkVenueSignToday($userId)) {
                    // 有未来场馆预约，设置为已签到状态
                    $filmSubscribeData['subscribe_state'] = '4';
                } else {
                    // 无未来场馆预约，设置为未签到状态
                    $filmSubscribeData['subscribe_state'] = '1';
                }
            }

            $filmSubscribeList[] = $filmSubscribeData;
        }

        // 开始事务处理
        \think\Db::startTrans();
    
        // 检查并扣减库存
        $this->checkAndReduceInventory($filmSeeionId, count($userLinkmanIds));

        // 批量插入预约记录
        $result = FilmSubscribe::insertAll($filmSubscribeList);

        if (!$result) {
            return $this->error('预约记录插入失败');   
        }

        \think\Db::commit();
        return $batchNumber;

    }

    /**
     * 检查用户是否有未来的场馆预约 - 完全对应Java版本personalVenueSubscribeEstimate
     * @param int $userId 用户ID
     * @return bool
     */
    private function checkVenueSignToday($userId)
    {
        // 完全对应Java版本的personalVenueSubscribeEstimate SQL
        $currentTime = date('Y-m-d H:i:s');

        $sql = "SELECT v.id, vs.user_id AS userId,
                       vs.sign_state AS signState,
                       v.venue_start_time AS venueStartTime,
                       v.venue_end_time AS venueEndTime
                FROM venue v, venue_subscribe vs
                WHERE v.id = vs.venue_id
                  AND vs.user_id = ?
                  AND vs.del_flag = '0'
                  AND date_format(v.venue_end_time,'%Y-%m-%d %H:%i:%s') > date_format(?,'%Y-%m-%d %H:%i:%s')
                ORDER BY v.venue_start_time ASC";

        $result = \think\Db::query($sql, [$userId, $currentTime]);

        return !empty($result);
    }

    /**
     * 检查并扣减库存
     * @param int $filmSeeionId 影片场次ID
     * @param int $count 预约人数
     * @throws \Exception
     */
    private function checkAndReduceInventory($filmSeeionId, $count)
    {
        // 获取当前库存
        $filmSession = \app\common\model\FilmSession::where('id', $filmSeeionId)->find();

        if (!$filmSession) {
            return $this->error('影片场次不存在');   
        }

        $currentInventory = $filmSession['inventory_votes'];

        // 检查库存是否足够
        if ($currentInventory < $count) {
              return $this->error('库存不足.预约失败');   
        }

        // 扣减库存
        $newInventory = $currentInventory - $count;
        $result = \app\common\model\FilmSession::where('id', $filmSeeionId)
            ->update(['inventory_votes' => $newInventory]);

        if (!$result) {
              return $this->error('库存扣减失败');   
        }
    }

    /**
     * 生成UUID
     * @return string
     */
    private function generateUuid()
    {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * 根据排片日期查询影片场次排期 - 对应Java版本selectByFilmArrangedDate方法
     * @ApiTitle    (根据排片日期查询影片场次排期)
     * @ApiSummary  (根据指定的排片日期和影片状态查询影片场次排期信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/fileSession/selectByFilmArrangedDate)
     * @ApiParams   (name="filmArrangedDate", type="string", required=true, description="排片日期，格式：YYYY-MM-DD")
     * @ApiParams   (name="filmState", type="string", required=false, description="影片状态(01/上架 02/下架)")
     */
    public function selectByFilmArrangedDate()
    {
        // 获取请求参数
        $filmArrangedDate = $this->request->param('filmArrangedDate');
        $filmState = $this->request->param('filmState', '');

        // 参数验证
        if (empty($filmArrangedDate)) {
            return $this->error('排片日期不能为空');
        }

        // 验证日期格式
        if (!$this->validateDate($filmArrangedDate)) {
            return $this->error('日期格式错误，请使用YYYY-MM-DD格式');
        }

        // 查询影片场次排期信息
        $scheduleList = $this->getFilmSessionScheduleByDate($filmArrangedDate, $filmState);

        // 格式化返回数据
        $formattedList = $this->formatFilmSessionScheduleData($scheduleList);

        return $this->success('查询成功', $formattedList);
    }

    /**
     * 根据排片日期查询影片场次排期数据 
     * @param string $filmArrangedDate 排片日期
     * @param string $filmState 影片状态
     * @return array
     */
    private function getFilmSessionScheduleByDate($filmArrangedDate, $filmState = '')
    {
        // 构建查询条件
        $where = [
            'fs.film_arranged_date' => $filmArrangedDate,
            'f.del_flag' => '0',
            'fs.del_flag' => '0'
        ];

        // 如果指定了影片状态，添加到查询条件
        if (!empty($filmState)) {
            $where['fs.film_state'] = $filmState;
        }

        // 执行查询
        $scheduleList =  \app\common\model\Film::alias('f')
            ->join(['film_session' => 'fs'], 'fs.film_id = f.id')
            ->field([
                'f.film_type as filmType',
                'f.film_name as filmName',
                'f.film_time as filmTime',
                'f.file_introduce as fileIntroduce',
                'f.film_cover as filmCover',
                'fs.id',
                'fs.film_id as filmId',
                'fs.film_poll as filmPoll',
                'fs.film_state as filmState',
                'fs.film_start_time as filmStartTime',
                'fs.film_end_time as filmEndTime',
                'fs.film_arranged_date as filmArrangedDate',
                'fs.year as year',
                'fs.week as week',
                'fs.inventory_votes as inventoryVotes'
            ])
            ->where($where)
            ->select();
        return $scheduleList;
    }

    /**
     * 格式化影片场次排期数据 
     * @param array $scheduleList 原始数据
     * @return array 格式化后的数据
     */
    private function formatFilmSessionScheduleData($scheduleList)
    {
        foreach ($scheduleList as &$item) {
    
            if (isset($item['filmStartTime'])) {
                $item['filmStartTime'] = date('Y/m/d H:i:s', strtotime($item['filmStartTime']));
            }
            if (isset($item['filmEndTime'])) {
                $item['filmEndTime'] = date('Y/m/d H:i:s', strtotime($item['filmEndTime']));
            }
          
            if (isset($item['filmArrangedDate'])) {
                $item['filmArrangedDate'] = date('Y/m/d', strtotime($item['filmArrangedDate']));
            }
        }

        return $scheduleList;
    }

    /**
     * 验证日期格式
     * @param string $date 日期字符串
     * @return bool
     */
    private function validateDate($date)
    {
        $d = \DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId()
    {
        return   $this->auth->getUser()->user_id;
    }

    /**
     * 通过批次号进行签到操作 - 对应Java中FilmSubscribeMapper.check方法
     * SQL: update film_subscribe set subscribe_state = '4' where batch_number = #{batchNumber} and subscribe_state='1' and del_flag='0'
     *
     * @param string $batchNumber 批次号
     * @return int 影响的行数
     */
    private function checkByBatchNumber($batchNumber)
    {
        
        return FilmSubscribe::where('batch_number', $batchNumber)
            ->where('subscribe_state', '1')
            ->where('del_flag', '0')
            ->update(['subscribe_state' => '4']);
    }

    /**
     * 根据批次号查询影片预约记录 - 对应Java中FilmSubscribeMapper.selectFilmSubscribeByBatchNumber
     * @param string $batchNumber 批次号
     * @return array
     */
    private function getFilmSubscribeByBatchNumber($batchNumber)
    {
        $sql = "SELECT id, film_seeion_id, user_id, user_linkman_id, subscribe_state, subscribe_type, batch_number, is_surplus
                FROM film_subscribe
                WHERE batch_number = ?
                AND subscribe_state IN ('4','5')
                AND del_flag = '0'";

        return \think\Db::query($sql, [$batchNumber]);
    }

    /**
     * 根据ID查询联系人信息 - 对应Java中UserLinkmanMapper.selectUserLinkmanById
     * @param int $linkmanId 联系人ID
     * @return array|null
     */
    private function getUserLinkmanById($linkmanId)
    {
        $sql = "SELECT linkman_name, linkman_phone, linkman_age, linkman_certificate, linkman_certificate_type, del_flag
                FROM user_linkman
                WHERE id = ?";

        $result = \think\Db::query($sql, [$linkmanId]);
        return !empty($result) ? $result[0] : null;
    }

    /**
     * 生成4位随机数 - 对应Java中RandomUtils.getFourBitRandom()
     * @return string
     */
    private function getFourBitRandom()
    {
        return str_pad(mt_rand(0, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * 获取当前时间 - 对应Java中Coding.getCurrentTime()
     * @return string
     */
    private function getCurrentTime()
    {
        return date('Y-m-d H:i:s');
    }

    /**
     * 加密并转换为16进制 - 对应Java中Coding.encryptDecryptHex()
     * @param string $src 源字符串
     * @return string 加密后的16进制字符串
     */
    private function encryptDecryptHex($src)
    {
        try {
            // 转换为GBK编码的字节数组
            $buf = iconv('UTF-8', 'GBK//IGNORE', $src);
            $bufBytes = [];
            for ($i = 0; $i < strlen($buf); $i++) {
                $bufBytes[] = ord($buf[$i]);
            }

            // 执行加密解密
            $encryptedBytes = \app\common\library\DesEncryption::encryptDecryptBuf($bufBytes);

            // 转换为16进制字符串
            return $this->byte2hex($encryptedBytes);
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * 字节数组转16进制字符串
     * @param array $bytes 字节数组
     * @return string 16进制字符串
     */
    private function byte2hex($bytes)
    {
        $hex = '';
        foreach ($bytes as $byte) {
            $hex .= sprintf('%02X', $byte);
        }
        return $hex;
    }
}