<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use app\common\model\CourseSubscribe;
use app\common\model\FilmSubscribe;
use app\common\model\VenueSubscribe;
use think\Db;
use think\Validate;
use think\Cache;


class UserLinkman extends Api
{
    protected $model = null;
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\UserLinkman;
    }

    /**
     * 获取联系人列表（分页）
     * GET: /admin/userlinkman/list
     */
    public function list()
    {
        $user = $this->auth->getUser();

        // 构建查询条件
        $where = [
            'user_id' => ['=', $user->user_id],
            'del_flag' => ['=', '0'],
        ];

        // 查询数据
        $list = $this->model
            ->field('id, user_id, linkman_name, linkman_phone, linkman_age, linkman_certificate, linkman_certificate_type')
            ->where($where)
            ->order('id desc')
            ->paginate();

        $rows = $list->items();
        foreach ($rows as &$item) {
            $item = [
                'id' => $item['id'],
                'userId' => $item['user_id'],
                'linkmanName' => !empty($item['linkman_name']) ? desensitize_name($item['linkman_name']) : '',
                'linkmanPhone' => !empty($item['linkman_phone']) ? desensitize_phone($item['linkman_phone']) : '',
                'linkmanAge' => $item['linkman_age'],
                'linkmanCertificate' => !empty($item['linkman_certificate']) ? desensitize_certificate($item['linkman_certificate']) : '',
                'linkmanCertificateType' => $item['linkman_certificate_type'],
            ];
        }

        return $this->success('获取成功', $rows ?? []);
    }


    /**
     * 添加联系人 - 对应Java中的/auth/linkman/add接口
     * 
     * @ApiTitle    (添加联系人)
     * @ApiSummary  (添加新的联系人信息)
     * @ApiMethod   (POST)
     * @ApiRoute    (/apitp/userlinkman/add)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="linkmanName", type="string", required=true, description="联系人姓名")
     * @ApiParams   (name="linkmanCertificate", type="string", required=true, description="联系人证件号码")
     * @ApiParams   (name="linkmanCertificateType", type="string", required=true, description="联系人证件类型(0=身份证,1=其他,2=港澳台居民居住证,3=其他)")
     * @ApiParams   (name="linkmanPhone", type="string", required=true, description="联系人手机号")
     * @ApiParams   (name="linkmanAge", type="integer", required=false, description="联系人年龄")
     */
    public function add()
    {  
      
        // 获取请求参数 
        $data = $this->request->param();
        
        // 参数验证 
        $validate = new Validate([
            'linkmanName' => 'require|max:50',
            'linkmanCertificate' => 'require|max:50',
            'linkmanCertificateType' => 'require|in:0,1,2,3',
            'linkmanPhone' => 'require|max:20'
        ], [
            'linkmanName.require' => '联系人名称不能为空',
            'linkmanName.max' => '联系人名称不能超过50个字符',
            'linkmanCertificate.require' => '联系人证件号码不能为空',
            'linkmanCertificate.max' => '证件号码不能超过50个字符',
            'linkmanCertificateType.require' => '联系人证件类型不能为空',
            'linkmanCertificateType.in' => '证件类型错误!',
            'linkmanPhone.require' => '手机号不能为空',
            'linkmanPhone.max' => '手机号不能超过20个字符'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        // 手机号格式验证 
        if (!$this->isValidPhone($data['linkmanPhone'])) {
            return $this->error('请输入正确的手机号码');
        }

        // 证件号码格式验证
        if (!$this->isValidIdCard($data['linkmanCertificate'])) {
            return $this->error('证件号码不合规');
        }
        // 调用业务逻辑处理 
        $userId = $this->auth->user_id;
        $result = $this->insertUserLinkman($userId, $data);
        
        if ($result > 0) {
            return $this->success('添加成功', $result);
        } else {
            return $this->error('添加失败');
        }
     
    }





    /**
     * 手机号格式验证 - 对应Java中的Coding.isPhone()
     * @param string $phone
     * @return bool
     */
    private function isValidPhone($phone)
    {
        // 中国大陆手机号验证
        return preg_match('/^1[3-9]\d{9}$/', $phone);
    }

    /**
     * 证件号码格式验证 - 对应Java中的IdCardUtil.isIdcard()
     * @param string $idCard
     * @return bool
     */
    private function isValidIdCard($idCard)
    {
        // 18位身份证号码验证
        if (strlen($idCard) == 18) {
            return $this->validateIdCard($idCard);
        }
        // 15位身份证号码验证
        // if (strlen($idCard) == 15) {
        //     return preg_match('/^[1-9]\d{5}\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}$/', $idCard);
        // }
        // 港澳台等其他证件，长度在6-20位之间
        if (strlen($idCard) >= 6 && strlen($idCard) <= 20) {
            return preg_match('/^[A-Za-z0-9]+$/', $idCard);
        }
        return false;
    } 

    private  function validateIdCard( $idCard)
    {
        // 1. 长度及格式校验
        if (!preg_match('/^\d{17}(\d|x)$/i', $idCard)) {
            return false;
        }

        // 2. 地址码校验（简单校验，只校验省份代码）
        $provinceCode = substr($idCard, 0, 2);
        $provinceCodes = [
            '11', '12', '13', '14', '15', '21', '22', '23', '31', '32', '33', '34', '35', '36', '37', '41',
            '42', '43', '44', '45', '46', '50', '51', '52', '53', '54', '61', '62', '63', '64', '65', '71',
            '81', '82', '91'
        ];
        if (!in_array($provinceCode, $provinceCodes)) {
            return false;
        }

        // 3. 出生日期校验
        $birthDate = substr($idCard, 6, 8);
        $year = (int) substr($birthDate, 0, 4);
        $month = (int) substr($birthDate, 4, 2);
        $day = (int) substr($birthDate, 6, 2);

        if ($year < 1900 || $year > date('Y')) {
            return false;
        }

        if ($month < 1 || $month > 12) {
            return false;
        }

        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);
        if ($day < 1 || $day > $daysInMonth) {
            return false;
        }

        // 4. 校验码校验
        // 系数
        $factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        // 校验码对应值
        $verifyNumberList = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

        $checksum = 0;
        for ($i = 0; $i < 17; $i++) {
            $checksum += (int) $idCard[$i] * $factor[$i];
        }

        $mod = $checksum % 11;
        $verifyNumber = $verifyNumberList[$mod];

        if (strtoupper($idCard[17]) !== $verifyNumber) {
            return false;
        }

        return true;
    }

    /**
     * 验证联系人是否存在且属于当前用户 - 对应Java中的selectUserLinkmanExist()
     * @param int $userId
     * @param int $id
     * @return bool
     */
    private function selectUserLinkmanExist($userId, $id)
    {
        $linkman = $this->model
            ->where('id', $id)
            ->where('user_id', $userId)
            ->where('del_flag', '0')
            ->find();
        
        return $linkman ? true : false;
    }

    /**
     * 更新联系人 - 对应Java中的UserLinkmanServiceImpl.updateUserLinkman()
     * @param array $data
     * @return int
     */
    private function updateUserLinkman($data)
    {
        // 构建更新数据 - 对应Java中的BeanUtils.copyProperties()
        $updateData = [
            'linkman_name' => $data['linkmanName'],
            'linkman_certificate' => $data['linkmanCertificate'],
            'linkman_certificate_type' => $data['linkmanCertificateType'],
            'linkman_age' => intval($data['linkmanAge']),
            'update_time' => date('Y-m-d H:i:s')
        ];

        // 年龄重新计算 - 对应Java中的年龄计算逻辑
        // 注意：Java中edit接口只在身份证类型("0")时重新计算年龄
        if ($data['linkmanCertificateType'] == '0') {
            $age = $this->getAgeByIdCard($data['linkmanCertificate']);
            if ($age > 0) {
                $updateData['linkman_age'] = $age;
            }
        }

        // 更新数据库
        $result = $this->model
            ->where('id', $data['id'])
            ->where('user_id', $data['userId'])
            ->update($updateData);
        
        return $result;
    }


    /**
     * 插入联系人 - 对应Java中的UserLinkmanServiceImpl.insertUserLinkman()
     * @param int $userId
     * @param array $data
     * @return int
     */
    private function insertUserLinkman($userId, $data)
    {
        // 构建数据库记录 
        $linkmanData = [
            'user_id' => $userId,
            'linkman_name' => $data['linkmanName'],
            'linkman_certificate' => $data['linkmanCertificate'],
            'linkman_certificate_type' => $data['linkmanCertificateType'],
            'linkman_phone' => $data['linkmanPhone'],
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
            'del_flag' => '0'
        ];

        // 年龄处理 
        if (isset($data['linkmanAge']) && is_numeric($data['linkmanAge'])) {
            $linkmanData['linkman_age'] = intval($data['linkmanAge']);
        } else {
            // 自动计算年龄（身份证或港澳台居民居住证）
            if ($data['linkmanCertificateType'] == '0' || $data['linkmanCertificateType'] == '2') {
                $age = $this->getAgeByIdCard($data['linkmanCertificate']);
                if ($age > 0) {
                    $linkmanData['linkman_age'] = $age;
                }
            }
        }
        // 插入数据库
        return $this->model->insertGetId($linkmanData);
    }

    /**
     * 根据身份证号计算年龄 - 对应Java中的IdcardUtil.getAgeByIdCard()
     * @param string $idCard
     * @return int
     */
    private function getAgeByIdCard($idCard)
    {
        if (strlen($idCard) == 18) {
            $birthYear = substr($idCard, 6, 4);
            $birthMonth = substr($idCard, 10, 2);
            $birthDay = substr($idCard, 12, 2);
        } elseif (strlen($idCard) == 15) {
            $birthYear = '19' . substr($idCard, 6, 2);
            $birthMonth = substr($idCard, 8, 2);
            $birthDay = substr($idCard, 10, 2);
        } else {
            return 0;
        }

        $birthDate = $birthYear . '-' . $birthMonth . '-' . $birthDay;
        $birthTimestamp = strtotime($birthDate);
        
        if ($birthTimestamp === false) {
            return 0;
        }

        $age = floor((time() - $birthTimestamp) / (365 * 24 * 3600));
        return $age > 0 && $age < 150 ? $age : 0;
    }

   




    /**
     * 编辑联系人 - 对应Java中的/auth/linkman/edit接口
     * 
     * @ApiTitle    (编辑联系人)
     * @ApiSummary  (编辑联系人信息)
     * @ApiMethod   (PUT)
     * @ApiRoute    (/apitp/userlinkman/edit)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="id", type="integer", required=true, description="联系人ID")
     * @ApiParams   (name="userId", type="integer", required=true, description="用户ID")
     * @ApiParams   (name="linkmanName", type="string", required=true, description="联系人姓名")
     * @ApiParams   (name="linkmanCertificate", type="string", required=true, description="联系人证件号码")
     * @ApiParams   (name="linkmanCertificateType", type="string", required=true, description="联系人证件类型")
     * @ApiParams   (name="linkmanAge", type="integer", required=true, description="联系人年龄")
     */
    public function edit()
    {

        // 获取用户ID - 
        $user = $this->auth->getUser();
        $currentUserId = $user->user_id;
        // 获取请求参数 -
        $data = $this->request->param();
        
        // 参数验证 
        $validate = new Validate([
            'id' => 'require|integer',
            'userId' => 'require|integer',
            'linkmanName' => 'require|max:50',
            'linkmanCertificate' => 'require|max:50',
            'linkmanCertificateType' => 'require|max:10',
            'linkmanAge' => 'require|integer'
        ], [
            'id.require' => '联系人ID不能为空',
            'id.integer' => '联系人ID必须是整数',
            'userId.require' => '用户ID不能为空',
            'userId.integer' => '用户ID必须是整数',
            'linkmanName.require' => '联系人姓名不能为空',
            'linkmanName.max' => '联系人姓名不能超过50个字符',
            'linkmanCertificate.require' => '联系人证件号码不能为空',
            'linkmanCertificate.max' => '证件号码不能超过50个字符',
            'linkmanCertificateType.require' => '联系人证件类型不能为空',
            'linkmanAge.require' => '联系人年龄不能为空',
            'linkmanAge.integer' => '联系人年龄必须是整数'
        ]);

        if (!$validate->check($data)) {
            return $this->error($validate->getError());
        }

        // 权限验证
        if ($currentUserId != $data['userId']) {
            return $this->error('越权编辑联系人');
        }

        // 验证联系人是否存在且属于当前用户 
        $existResult = $this->selectUserLinkmanExist($currentUserId, $data['id']);
        if (!$existResult) {
            return $this->error('越权访问联系人！或联系人不存在');
        }

        // 证件号码格式验证 
        if (!$this->isValidIdCard($data['linkmanCertificate'])) {
            return $this->error('证件号码不合规');
        }

        // 调用业务逻辑处理 
        $result = $this->updateUserLinkman($data);
        
        if ($result > 0) {
            return $this->success('更新成功', $result);
        } else {
            return $this->error('更新失败');
        }

    }

    /**
     * 检查签到状态 - 获取用户中心消息
     * @ApiTitle    (检查签到状态)
     * @ApiSummary  (获取用户中心消息统计信息，包括影片、课程、场馆的预约数量)
     * @ApiMethod   (GET)
     * @ApiRoute    (/auth/linkman/isSign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function isSign()
    {
        // 获取用户ID
        $userId = $this->auth->getUser()->user_id;
        // 获取用户中心消息统计
        $userMsgVo = $this->getUserCenterMsg($userId);

        return $this->success('获取成功', $userMsgVo);
    }

    /**
     * 获取用户中心消息统计 - 对应Java中的getUserCenterMsg方法
     * @param int $userId 用户ID
     * @return array
     */
    private function getUserCenterMsg($userId)
    {
        $currentTime = date('Y-m-d H:i:s');

        // 1. 统计课程预约数量（未签到且未过期的）
        $courseCount = CourseSubscribe::alias('cs')
            ->join(['course_session' => 'c'], 'c.id = cs.course_session_id')
            ->where('cs.user_id', $userId)
            ->where('cs.sign_state', '0')
            ->where('cs.del_flag', '0')
            ->where('cs.subscribe_state', '1')
            ->where('c.course_end_time', '>=', $currentTime)
            ->count('DISTINCT cs.course_session_id');

        // 2. 统计场馆预约数量（未签到且未过期的）
        $venueCount = VenueSubscribe::alias('vs')
            ->join(['venue' => 'v'], 'v.id = vs.venue_id')
            ->where('vs.user_id', $userId)
            ->where('vs.sign_state', '0')
            ->where('vs.del_flag', '0')
            ->where('vs.subscribe_state', '1')
            ->where('v.venue_end_time', '>=', $currentTime)
            ->count('DISTINCT vs.venue_id');

        // 3. 统计影片预约数量（状态为1或4的）
        $filmCount = FilmSubscribe::where('user_id', $userId)
            ->whereIn('subscribe_state', ['1', '4'])
            ->where('del_flag', '0')
            ->count('DISTINCT film_seeion_id');

        // 构建返回数据结构，对应Java中的UserMsgVo
        return [
            'film' => [
                'num' => (int)$filmCount
            ],
            'course' => [
                'num' => (int)$courseCount
            ],
            'venue' => [
                'num' => (int)$venueCount
            ]
        ];
    }

}

