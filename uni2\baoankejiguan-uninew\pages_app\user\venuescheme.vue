<template>
	<view class="venue_scheme">
		<Header menuClass="bor" :isBack="true" :isShowHome="true" title="个人中心" :background="`#fff`" :color="'#000'">
		</Header>
		<scroll-view class="for_put_scroll" scroll-y @scrolltolower="pageChange()">
			<view class="scheme" :style="{
				paddingBottom : menuButtonInfo.height + menuButtonInfo.top + 38 + 'px'
			}">
				<view class="scheme_item" v-for="(item,index) in venueList" :key="index">
					<view class="scheme_state_tips">
						<view class="title">
							宝安科技馆 入馆预约
						</view>
						<view class="tip_content">
							<view class="tip_item">
								<text class="ordinary">预约日期：</text> <text class="imp">{{item.date}}</text>
							</view>
							<view class="tip_item">
								<text class="ordinary">入馆时间：</text> <text class="imp">{{item.isMorning?'上午场':'下午场'}} {{item.times}}</text>
							</view>
							<view class="tip_item">
								<text class="ordinary">预约人数：</text> <text class="sp">{{item.peopleCount}}人</text>
							</view>
						</view>
					</view>
					<view class="button_list">
						<view :class="['normal',item.signState === '1' ? 'isSigned' : '']" v-if="item.flag === '1'">
							<button type="default" class="cancel_order" @click="cancelOrder(item.venueId)">取消预约</button>
							<button type="default" class="view_voucher" @click="jumpToDes(item.venueId)">查看凭证</button>
						</view>
						<button v-else-if="item.flag === '0' || item.flag === '2'" type="default"
							class="cancel_ed">{{item.flag === '0' ? flagState[item.flag ] : flagState[item.flag][item.signState]}}</button>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		header
	} from '@/component/header.vue';
	import Utils from '@/utils/index.js';
	export default {
		data() {
			return {
				pageNum: 1,
				pageSize: 5,
				total: 0,
				venueList: [],
				flagState: {
					'0': '已取消',
					'1': ['未签到', '已签到'],
					'2': ['已失效', '已完成']
				},
				menuButtonInfo: {}
			}
		},
		components: {
			header
		},
		onShow() {
			this.pageNum = 1;
			this.pageSize = 5;
			this.total = 0;
			this.venueList = [];
			this.getList();
			this.menuButtonInfo = uni.getMenuButtonBoundingClientRect();
		},
		methods: {
			jumpToDes(id) {
				uni.navigateTo({
					url: `/pages_app/schemesuccess/venuesuccess?id=${id}`
				})
			},
			getList() {
				this.$myRequest({
					// url: '/auth/venue/showMySign',
					url: '/apitp/venue/showMySign',
					data: {
						pageNum: this.pageNum,
						pageSize: this.pageSize
					}
				}).then(res => {
					if (res.data.code === 200) {
						let dataList = res.data.data.rows;
						this.total = res.data.data.total;
						dataList.forEach(item => {
							item['isMorning'] = new Date(item.venueStartTime).getHours() < 12
							item['times'] = Utils.changeTime(item.venueStartTime) + '-' + Utils.changeTime(
								item.venueEndTime);
							item['date'] = item.venueStartTime.slice(0, 10).replace(/-/g, '.');
							this.venueList.push(item);
						})
					}
				})
			},
			pageChange() {
				if (this.venueList.length >= this.total) {
					return;
				}
				this.pageNum++;
				this.getList();
			},
			cancelOrder(id) {
	  			uni.navigateTo({
					url: `/pages_app/schemesuccess/venuecancel?venueId=${id}`
				})
				// uni.showModal({
				// 	title: "是否取消预约",
				// 	showCancel: true,
				// 	success: (state) => {
				// 		if (state.confirm) {
				// 			this.$myRequest({
				// 				url: '/auth/venue/cancelSign',
				// 				data: {
				// 					venueId: id
				// 				}
				// 			}).then(res => {
				// 				if (res.data.code === 200) {
				// 					this.venueList.forEach(item => {
				// 						if (item.venueId === id) {
				// 							item.flag = '0';
				// 						}
				// 					})
				// 					// this.venueList = []
				// 					uni.showToast({
				// 						title: '取消预约成功',
				// 						duration: 2000
				// 					});
				// 					// this.getList()
				// 				}
				// 			})
				// 		} else if (state.cancel) {

				// 		}
				// 	}
				// })
			}
		}
	}
</script>

<style lang="less" scoped>
	.venue_scheme {
		width: 100%;
		height: 100vh;
		position: relative;
		font-family: "PingFang SC";
		background: #F3F4F6;

		.for_put_scroll {
			width: 100%;
			height: 100vh;

			.scheme {
				padding: 28upx;
				min-height: 90vh;
				box-sizing: border-box;

				.scheme_item {
					width: 100%;
					height: auto;
					background-color: #fff;
					padding: 19upx 19upx 38upx 19upx;
					border-radius: 10upx;
					box-sizing: border-box;
					margin-bottom: 19upx;

					&>view {
						box-sizing: border-box;
					}

					.scheme_state_tips {
						margin-bottom: 30upx;

						.title {
							font-size: 35upx;
							font-weight: 600;
							color: #000000;
							margin-bottom: 29upx;
						}

						.tip_content {
							width: 100%;
							height: auto;
							background-color: #F3F4F6;
							border-radius: 10upx;
							box-sizing: border-box;
							padding: 17upx 0 15upx 25upx;

							.tip_item {
								margin-bottom: 19upx;

								&:last-of-type {
									margin-bottom: 0;
								}
							}

							.imp {
								font-size: 29upx;
								color: #000000;
							}

							.ordinary {
								font-size: 27upx;
								color: #6F6F6F;
							}

							.sp {
								font-size: 29upx;
								color: #FFBA38;
							}

						}
					}

					.button_list {
						width: 100%;
						height: 77upx;
						display: flex;
						justify-content: space-around;

						button::after {
							border: none;
							background-color: none;
						}

						.normal {
							width: 100%;
							height: 100%;
							display: flex;
							justify-content: space-around;

							.cancel_order,
							.view_voucher {
								width: 308upx;
								height: 100%;
								text-align: center;
								line-height: 77upx;
								font-size: 35upx;
								color: #FFFFFF;
								background-color: #FFBA38;
								box-shadow: 0px 6px 8px rgba(255, 186, 56, 0.34);

								&::after {
									outline: none;
									border: 0px !important;
								}
							}

							.view_voucher {
								background-color: #5CB7FF;
								box-shadow: 0px 6px 8px rgba(92, 183, 255, 0.34);
							}

							//如果已经签到了的话
							&.isSigned {
								.cancel_order {
									display: none;
								}

								.view_voucher {
									width: 100%;
								}
							}
						}

						.cancel_ed {
							width: 100%;
							height: 100%;
							line-height: 77upx;
							background: #DADADA;
							box-shadow: 0px 6px 12px rgba(218, 218, 218, 0.34);
							color: #fff;
						}

					}

				}

			}
		}
	}
</style>
