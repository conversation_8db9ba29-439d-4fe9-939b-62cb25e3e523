<?php

namespace app\common\model;

use think\Model;

class UserLinkman extends Model
{
    // 表名
    protected $table = 'user_linkman';

    // 定义时间戳字段名
    protected $autoWriteTimestamp = 'datetime'; // 改为 'datetime'
    protected $dateFormat = 'Y-m-d H:i:s'; // 定义输出格式
    protected $createTime = 'create_time'; // 创建时间字段名
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [];

    /**
     * 校验联系人 ID 是否都属于指定用户
     * @param int $userId 用户ID
     * @param array $linkIds 提交的联系人ID数组
     * @return bool
     */
    public static function validateLinkmansBelongToUser($userId, array $linkIds): bool
    {
        if (empty($linkIds)) return false;

        $ownedIds = self::where('user_id', $userId)->column('id');
        return empty(array_diff($linkIds, $ownedIds));
    }
}
