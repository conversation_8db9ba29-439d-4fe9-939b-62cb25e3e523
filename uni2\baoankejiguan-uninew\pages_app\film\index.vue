<template>
  <view class="view_order">
    <view class="content">
      <Header
        menuClass="df"
        :isBack="true"
        :isShowHome="true"
        title="观影预约"
        :background="`transparent`"
      >
      </Header>
      <view class="titlebar"></view>
      <view class="main">
        <view class="calendarContent">
          <uni-calendar
            :date="timeSection.start"
            :insert="true"
            :lunar="false"
            :showMonth="false"
            :start-date="timeSection.start"
            :end-date="timeSection.end"
            :selected="selected"
            @change="getFilmDetail"
          />
        </view>
        <view class="line">
          <view
            class="line_item"
            v-for="(item, index) in 20"
            :key="index"
          ></view>
        </view>
        <view class="flimContent">
          <view class="showFlimTitle">影片列表</view>
          <view class="filmList">
            <!-- <template v-if="filmList.length && filmList[filmList.length - 1].isShow"> -->
            <template v-if="hasAnyShow">
              <view
                :class="[film.isShow ? '' : 'disNone', 'fileItem']"
                v-for="(film, index) in filmList"
                :key="film.id"
              >
                <view class="itemLeft">
                  <image
                    :src="film.filmCover"
                    mode="aspectFill"
                    class="cover"
                  ></image>
                  <view class="fileInfo">
                    <text class="filmName">{{ film.filmName }}</text>
                    <text class="filmType"
                      >类型：{{ filmTypeList[film.filmType - 1] }}</text
                    >
                    <text class="filmTime"
                      >时间：{{ film.filmStartTime }}-{{
                        film.filmEndTime
                      }}</text
                    >
                  </view>
                </view>
                <view class="itemRight">
                  <!-- <text class="t_text">{{countDownList[index]}}</text> -->
                  <view class="ticketType" v-if="countDownFlagList[index]">
                    <text class="n_text">倒计时</text>
                    <text class="t_text">{{ countDownList[index] }}</text>
                  </view>

                  <view
                    :class="[
                      'order_button',
                      countDownFlagList[index] ? 'green' : '',
                      film.inventoryVotes == 0 ? 'gray' : '',
                    ]"
                    @click="jumpToDes(film)"
                    v-if="film.bookNotEndFlag"
                  >
                    剩余:{{ film.inventoryVotes }}
                  </view>
                  <view class="order_gray_button" v-else>预约结束</view>
                </view>
              </view>
            </template>
            <view class="empty" v-else> 暂无影片数据 </view>
          </view>
        </view>
      </view>
      <view class="mask" v-show="isShowMask">
        <view class="maskContent">
          <view class="noticeTitle">观影须知</view>
          <view class="noticeView">
            <view
              class="noticeItem"
              v-for="(item, index) in noticeList"
              :key="index"
            >
              <view class="itemTitle">
                {{ item.title }}
              </view>
              <view
                class="itemContent"
                v-for="(childItem, childIndex) in item.view"
                :key="childIndex"
              >
                {{ childItem }}
              </view>
            </view>
          </view>
          <view :class="['agreeBtn', isShowGray ? 'gray' : '']" @click="agree">
            {{ readText }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Header from "@/component/header.vue";
import baseConfig from "@/utils/baseConfig.js";
import UniCalendar from "@/uni_modules/uni-calendar/components/uni-calendar/uni-calendar.vue";
import Utils from "@/utils/index.js";
import { checkLoginPromise } from "@/utils/auth.js";
export default {
  data() {
    return {
      iSzgm: baseConfig.iSzgm,
      loginReady: false,
      userInfo: {},
      noticeList: [
        { title: "一、观影对象", view: [] },
        { title: "二、观影登记", view: [] },
        { title: "三、入场审核", view: [] },
      ],
      times: null,
      readText: "确定已阅读（5s）",
      readTime: 5,
      film: {}, //点击预约所选择的影片对象
      filmList: [],
      filmTypeList: ["球幕电影", "4D电影"],
      timerList: [], // 定时器列表
      countDownList: [], // 倒计时列表
      countDownFlagList: [], // 是否显示倒计时
      timeSection: {
        start: "",
        end: "",
      },
      isShowGray: false,
      isShowMask: false,
      selected: [],
      fulldate: null,
      subscribeList: [{}],
    };
  },
  components: {
    Header,
    UniCalendar,
  },
  computed: {
  hasAnyShow() {
    return this.filmList.some(f => f.isShow);
  }
},
  created() {
    this.showMask(); //须知
    this.getTimeSection();
  },

  async onShow() {
    // if (!this.loginReady) {
    //   console.log("登录未完成，跳过 onShow 逻辑");
    //   return;
    // } else {
    //   this.getVenueInfo();
    // }
    try {
      // 如果没登录，引导登录
      // #ifdef MP-WEIXIN
      //需要定义 ：this.userInfo + this.loginReady
      await checkLoginPromise(this); // 👈 登录完成后再继续执行
      console.log("登录后的 userInfo:", this.userInfo);

      // #endif

      // #ifdef H5
      // 只在 H5 平台编译执行的代码
      console.log("当前是 H5 环境");
      // #endif

      //执行业务
      this.getVenueInfo();
    } catch (e) {
      console.warn("登录失败或取消", e);
    }
  },

  onHide() {
    this.timerList.forEach((item) => {
      clearInterval(item);
    });
  },
  onUnload() {
    this.timerList.forEach((item) => {
      clearInterval(item);
    });
  },
  methods: {
    //电影须知，
    getlist() {
      this.$myRequest({
        // url: "/admin/entrance_announcement/2",
        url: "/apitp/Announcement/getEntranceInfo",
        data: {
          id: 2,
        },
        method: "get",
      }).then((res) => {
        if (res.data.code === 200) {
          let ent = res.data.data;
          let audit = ent.entranceAudit.split("\n");
          let object = ent.entranceObject.split("\n");
          let register = ent.entranceRegister.split("\n");
          this.noticeList[0].view = object;
          this.noticeList[1].view = register;
          this.noticeList[2].view = audit;
        }
      });
    },
    //计算当前时间
    getTimeSection() {
      let time = new Date();
      var oneDayTime = 24 * 60 * 60 * 1000;
      let start =
        time.getFullYear() + "-" + (time.getMonth() + 1) + "-" + time.getDate();
      let weekAfter = new Date(time.getTime() + oneDayTime * 6);
      let end =
        weekAfter.getFullYear() +
        "-" +
        (weekAfter.getMonth() + 1) +
        "-" +
        weekAfter.getDate();
      this.timeSection.start = start;
      this.timeSection.end = end;
    },

    showMask() {
      this.getlist();


      this.isShowMask = true; //设置为 true 才会显示遮罩层
	          const accountInfo = uni.getAccountInfoSync();
	        this.isShowMask = accountInfo.miniProgram.envVersion === 'develop' ? false : true;
      this.isShowGray = true;
      this.readTime = 5;
      this.times = setInterval(() => {
        if (this.readTime === 1) {
          this.readText = "确定已阅读";
          this.isShowGray = false;
          clearInterval(this.times);
        } else {
          this.readTime--;
          this.readText = `确定已阅读（${this.readTime}s）`;
        }
      }, 1000);
    },
	agree() {
      if (this.readTime !== 1) return;
      setTimeout(() => {
        this.isShowMask = false;
      }, 300);
    },
    //获取闭馆标记 + 场次初始时间段
    async getVenueInfo() {
      let res = await this.$myRequest({
        // url: '/auth/venue/getVenueInfo',
        url: "/apitp/venue/getVenueInfo",
      });
      if (res.data.code === 200) {
        let dataList = res.data.data;
        this.selected = []; // 初始化标记数组
        dataList.forEach((item) => {
          var day = Utils.changeTime(item.day, true);
          if (item.isClose === "0") {
            this.selected.push({
              date: day,
              info: "闭馆",
            });
          }
        });
        // 获取第一个开放日（isClose = 1）  作为起始预约时间
        for (var i = 0; i < dataList.length; i++) {
          if (dataList[i].isClose === "1") {
            this.timeSection.start = Utils.changeTime(dataList[i].day, true);
            break;
          }
        }
        if (this.timeSection.start) {
          // 拉取当天详细 电影列表
          this.getFilmDetail({
            fulldate: this.fulldate || this.timeSection.start,
          });
        }
      }
    },
    //获取电影列表
    async getFilmDetail(e) {
      this.timerList.forEach((item) => {
        clearInterval(item);
      });
      this.$myRequest({
        // url: "/web/fileSession/selectByFilmArrangedDate",
        url: "/apitp/Filmsession/selectByFilmArrangedDate",
        data: {
          filmArrangedDate: e.fulldate,
          filmState: "01",
        },
      }).then((res) => {
        this.fulldate = e.fulldate;
           //排序
        let data = res.data.data.sort((a, b) => {
          return (
            new Date(a.filmStartTime.replace(/-/g, "/")) -
            new Date(b.filmStartTime.replace(/-/g, "/"))
          );
        });




		// // 初始化变量
		// function pad(n) {
		//   return n < 10 ? '0' + n : n;
		// }

		// function format(date) {
		//   const y = date.getFullYear();
		//   const m = pad(date.getMonth() + 1);
		//   const d = pad(date.getDate());
		//   const h = pad(date.getHours());
		//   const min = pad(date.getMinutes());
		//   const s = pad(date.getSeconds());
		//   return `${y}/${m}/${d} ${h}:${min}:${s}`;
		// }

		// function generateTestData() {
		//   const now = new Date();

		//   const makeItem = (offsetMinutes, id, name, intro) => {
		//     const start = new Date(now.getTime() + offsetMinutes * 60 * 1000);
		//     const end = new Date(start.getTime() + 40 * 60 * 1000); // 每场时长 40分钟
		//     return {
		//       filmType: "2",
		//       filmName: name,
		//       filmTime: 40,
		//       fileIntroduce: intro,
		//       filmCover: `https://example.com/test${id}.jpg`,
		//       id: id,
		//       filmId: 10000 + id,
		//       filmPoll: 0,
		//       filmState: "01",
		//       filmStartTime: format(start),
		//       filmEndTime: format(end),
		//       filmArrangedDate: `${start.getFullYear()}/${pad(start.getMonth() + 1)}/${pad(start.getDate())}`,
		//       year: start.getFullYear(),
		//       week: getWeekNumber(start),
		//       inventoryVotes: Math.floor(Math.random() * 20 + 1)
		//     };
		//   };

		//   // 五种典型场景
		//   return [
		//     makeItem(60, 1, "超30分钟", "测试：开场时间距离当前超过30分钟"),
		//     makeItem(20, 2, "进入倒计时", "测试：距离开场20分钟，进入倒计时区间"),
		//     makeItem(6, 31, "小于10分钟", "测试：距离开场5分钟，不可预约"),
		//     makeItem(5, 23, "小于10分钟", "测试：距离开场5分钟，不可预约"),
		//     makeItem(-1, 4, "刚刚开场", "测试：影片已开始"),
		//     makeItem(0, 5, "正好开场", "测试：影片开始时间等于当前时间")
		//   ];
		// }

		// // 获取年份中的周数
		// function getWeekNumber(date) {
		//   const oneJan = new Date(date.getFullYear(), 0, 1);
		//   const millisecsInDay = 86400000;
		//   return Math.ceil((((date - oneJan) / millisecsInDay) + oneJan.getDay() + 1) / 7);
		// }


		// let data1 = generateTestData(); // 生成符合格式的测试数据
		// //排序
		// let data =data1.sort((a, b) => {
		// 	return (
		// 	new Date(a.filmStartTime.replace(/-/g, "/")) -
		// 	new Date(b.filmStartTime.replace(/-/g, "/"))
		// 	);
		// });




        /**
         * cdc
         * 20221101
         */
        data.forEach((item, index) => {
			let nowTime = new Date().getTime();
          // 影片开始时间 - 30分钟 = 截止签到时间
		//   转成时间戳。
          let filmStartTime = new Date(            item.filmStartTime.replace(/-/g, "/")          ).getTime();
          let signEndTime = filmStartTime;
		// "2025/08/05 02:02:00" 转换 "2025-08-05 02:02"。
          item.filmStartTime = Utils.changeTime(            item.filmStartTime.replace(/-/g, "/"),            false          );
          item.filmEndTime = Utils.changeTime(            item.filmEndTime.replace(/-/g, "/"),            false          );

          //2021-01-01 00:00:00（1609430400000）
          let time = signEndTime - nowTime + 1609430400000;

          //每个影片倒计时标志（影片开始前30分钟）
          let countDownFlag =
            time > 1609430400000 && time < 1000 * 60 * 30 + 1609430400000;

          //影片开始，隐藏影片
          this.$set(item, "isShow", filmStartTime > nowTime);

          //倒计时05:00（5分钟内），按钮状态变成预约结束
          this.$set(item,"bookNotEndFlag",filmStartTime - 1000 * 60 * 10 > nowTime);

          let countDownTimer = setInterval(() => {
            let nowTime = new Date().getTime();
            this.$set(item, "isShow", filmStartTime > nowTime);
            this.$set(item,"bookNotEndFlag",filmStartTime - 1000 * 60 * 10 > nowTime);
            time -= 1000;
            countDownFlag =time > 1609430400000 && time < 1000 * 60 * 30 + 1609430400000;
            this.countDownList.splice(index, 1, this.date2MS(time));
            this.countDownFlagList.splice(index, 1, countDownFlag);
          }, 1000);

          this.timerList[index] = countDownTimer;
        });
	 this.filmList = data;
      });
    },
    date2MS(date) {
      let date1 = new Date(date);
      let m =
        date1.getMinutes() < 10 ? "0" + date1.getMinutes() : date1.getMinutes();
      let s =
        date1.getSeconds() < 10 ? "0" + date1.getSeconds() : date1.getSeconds();
      return m + ":" + s;
    },
    jumpToDes(film) {
    //   console.log(film);
      this.$myRequest({
        // url: "/web/fileSession/subscribeEstimate",
        url: "/apitp/FilmSession/subscribeEstimate",
        method: "get",
      }).then((res) => {
        if (res.data.code === 200) {
          // 成功逻辑
          const dataList = res.data.data;

          // 影片排片日期，格式统一为 2025-08-06
          const targetDate = film.filmArrangedDate.replace(/\//g, "-");
          // 判断是否存在匹配的预约记录
          const hasBooked = dataList.some(
            (item) => item.venueStartTime === targetDate
          );

          if (!hasBooked) {
            // 未预约，引导去预约
            uni.showModal({
              content: "请先进行参观预约!",
              confirmText: "去预约",
            //   showCancel: false,
              success: function (modalRes) {
                if (modalRes.confirm) {
                  uni.navigateTo({
                    url:
                      "/pages_app/entervenue/index?date=" + targetDate,
                  });
                }
              },
            });
          } else {
            uni.navigateTo({
              // url: `/pages/vieworder/filmdes?filmSessionId=${film.id}`,
              url: `/pages_app/film/filmdes?filmSessionId=${film.id}`,
            });
          }
        } else {
          // 错误提示
          uni.showToast({
            title: "异常：" + res.data.errMsg,
            icon: "error",
            duration: 2000,
          });
        }
      });
    },



    // async subscribe() {
    //   let res = await this.$myRequest({
    //     url: "/web/fileSession/subscribeEstimate",
    //   });
    //   this.subscribeList = res.data.data;
    //   // this.subscribeList.forEach((value) => {
    //   // 	this.selected.push({
    //   // 		date: value.venueStartTime,
    //   // 	});
    //   //})
    // },











  
  }
};
</script>

<style lang="less" scoped>
.view_order {
  width: 100%;
  height: 100vh;
  background: url("../../static/img/vieworder/initbg.png") no-repeat center top;
  background-color: blue;
  background-size: cover;
  color: #fff;
  position: relative;
  font-family: "PingFang SC";
  overflow: scroll;

  .content {
    width: 100%;
    height: auto;
    position: absolute;

    .titlebar {
      width: calc(~"100% - 30upx");
      height: 96upx;
      margin: 0 auto;
      background: url("../../static/img/vieworder/titlebar.png") no-repeat
        center top;
      background-size: cover;
      margin-top: 81upx;
    }

    .main {
      width: 654upx;
      height: auto;
      margin: -50upx auto 152upx auto;

      .calendarContent {
        width: 100%;
        height: 737upx;
        min-height: 720upx;
        margin: 0 auto;
        box-sizing: border-box;
        padding-top: 80upx;
        background: url(../../static/img/vieworder/content_body.png) no-repeat
          center center;
        background-size: 100% 100%;
      }

      // .line {
      // 	width: 80%;
      // 	height: 1upx;
      // 	position: relative;
      // 	left: 50%;
      // 	transform: translate(-50%, -50%);
      // 	display: flex;
      // 	justify-content: space-around;

      // 	.line_item {
      // 		width: 2%;
      // 		height: 100%;
      // 		background-color: #5f94dc;
      // 	}
      // }

      .flimContent {
        width: 100%;
        min-height: 550upx;
        padding-top: 25upx;
        background-color: #fff;
        position: relative;

        .showFlimTitle {
          font-size: 35upx;
          text-align: center;
          color: #000;
          font-weight: 600;
          margin-bottom: 19upx;
        }

        .filmList {
          width: 100%;
          height: auto;
          min-height: 480upx;
          padding: 0 19upx 16upx 19upx;
          box-sizing: border-box;
          overflow: hidden;

          .empty {
            color: #000;
            text-align: center;
          }

          .fileItem {
            width: 100%;
            height: 231upx;
            margin-bottom: 19upx;
            overflow: hidden;
            display: flex;
            justify-content: space-between;
            padding: 19upx;
            box-sizing: border-box;
            background-color: #f3f4f6;
            border-radius: 20upx;

            &:last-of-type {
              margin-bottom: 0;
            }

            &.disNone {
              display: none;
            }

            .itemLeft {
              width: auto;
              height: 100%;
              display: flex;
              align-items: center;

              .cover {
                width: 144upx;
                height: 192upx;
                border-radius: 10upx;
              }

              .fileInfo {
                height: 100%;
                padding-top: 8upx;
                box-sizing: border-box;
                margin-left: 20rpx;

                .filmName {
                  font-size: 29upx;
                  font-family: "PingFang SC";
                  font-weight: 600;
                  color: #000000;
                }

                .filmType,
                .filmTime {
                  display: block;
                  font-size: 23upx;
                  font-family: "PingFang SC";
                  color: #888888;
                }

                .filmType {
                  margin: 38upx 0 19upx 0;
                }

                .filmTime {
                }
              }
            }

            .itemRight {
              width: 163upx;
              // height: 100%;
              display: inline-block;
              position: relative;
              font-family: "PingFang SC";
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              align-items: center;

              .ticketType {
                height: 40upx;
                background: linear-gradient(180deg, #06e08b 0%, #02c975 100%);
                color: #f6f3f3;
                text-indent: 12upx;
                // margin-bottom: 67upx;
                border-radius: 0 20upx 0 0;
                position: absolute;
                top: -10%;
                right: -10%;
                padding-right: 39upx;
                line-height: 40upx;

                .n_text {
                  font-size: 16upx;
                  margin-right: 5upx;
                }

                .t_text {
                  font-size: 24upx;
                }
              }

              .order_button {
                width: 135upx;
                height: 58upx;
                line-height: 58upx;
                text-align: center;
                background: linear-gradient(180deg, #ffca5f 0%, #ffb33c 100%);
                box-shadow: 0upx 6upx 12upx rgba(255, 154, 54, 0.34);
                border-radius: 38upx;
                font-size: 26upx;

                &.green {
                  background: linear-gradient(180deg, #06e08b 0%, #02c975 100%);
                  box-shadow: 0px 6px 12px rgba(2, 202, 118, 0.34);
                }

                &.gray {
                  background: rgba(128, 128, 128, 0.6);
                  box-shadow: 0upx 6upx 12upx rgba(128, 128, 128, 0.34);
                }

                &.remain_ticket {
                  background: linear-gradient(180deg, #06e08b 0%, #02c975 100%);
                  box-shadow: 0px 6px 12px rgba(2, 202, 118, 0.34);
                }
              }

              .order_gray_button {
                width: 135upx;
                height: 58upx;
                line-height: 58upx;
                text-align: center;
                background: rgba(128, 128, 128, 0.6);
                box-shadow: 0upx 6upx 12upx rgba(128, 128, 128, 0.34);
                border-radius: 38upx;
                font-size: 26upx;
              }
            }
          }
        }

        &::after {
          content: "";
          width: 100%;
          height: 21upx;
          background: url(../../static/img/vieworder/content_foot.png) no-repeat
            center center;
          background-size: 100% 100%;
          position: absolute;
          bottom: -21upx;
          left: 0;
        }
      }
    }

    .mask {
      width: 100%;
      height: auto;
      background-color: rgba(0, 0, 0, 0.5);
      position: fixed;
      top: 0;
      bottom: 0;
      padding: 237upx 0;
      overflow: auto;

      .maskContent {
        width: 654upx;
        height: auto;
        background: #ffffff;
        border: 2upx solid #707070;
        border-radius: 19upx;
        margin: 0 auto;
        box-sizing: border-box;
        padding: 58upx 58upx 48upx 58upx;
        font-family: "PingFang SC";

        .noticeTitle {
          color: #1b1b1b;
          font-size: 35upx;
          font-weight: bold;
          margin-bottom: 60upx;
        }

        .noticeView {
          width: 100%;
          height: auto;
          text-align: justify;

          .noticeItem {
            width: 100%;
            height: auto;
            margin-bottom: 52upx;

            .itemTitle {
              color: #5d5d5d;
              font-size: 29upx;
              font-weight: 700;
              line-height: 50upx;
            }

            .itemContent {
              color: #888888;
              font-size: 27upx;
              line-height: 50upx;
            }
          }
        }

        .agreeBtn {
          width: 381upx;
          height: 77upx;
          text-align: center;
          line-height: 77upx;
          background: #5cb7ff;
          box-shadow: 0upx 6upx 12upx rgba(82, 162, 225, 0.34);
          border-radius: 10upx;
          font-size: 35upx;
          color: #fff;
          margin: 0 auto;

          &.gray {
            background-color: #888888;
            box-shadow: 0upx 6upx 12upx rgba(136, 136, 136, 0.34);
          }
        }
      }
    }
  }
}
</style>
