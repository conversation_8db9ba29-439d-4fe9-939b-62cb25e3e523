<?php

namespace app\common\model;

use think\Model;

class VenueSubscribe extends Model
{
    // 显式绑定 Java 项目的 MySQL 表名
    protected $table = 'venue_subscribe';


    // 模型类中
    protected $autoWriteTimestamp = 'datetime'; // 改为 'datetime'
    protected $dateFormat = 'Y-m-d H:i:s'; // 定义输出格式
    protected $createTime = 'create_time'; // 创建时间字段名
    protected $updateTime = 'update_time'; // 更新时间字段名
    /**
     * 检查用户是否已报名或签到
     * @param int $userId 用户ID
     * @param int $venueId 场馆ID
     * @param int $linkId 联系人ID
     * @return int 返回匹配的数量
     */
    public static function veryIfSignUp($userId, $venueId, $linkId)
    {
        return self::where('user_id', $userId)
            ->where('venue_id', $venueId)
            ->where('user_linkman_id', $linkId)
            ->where('del_flag', 0)
            ->count();
    }
}
