<template>
	<view class="addLink">
		<Header :isBack="true" :isShowHome="false" :title="'新增人员'" :color="'#000'">
		</Header>
		<view class="addBox">
			<view class="checkCardType">
				<view class="label">
					证件类型
				</view>
				<view class="checkItem-itm">
					<view class="checkItem" v-for="(item,index) in cardTypeList" @click="checkCardType(item.value)"
						:key="index">
						<view :class="['checkShow',index==form.linkmanCertificateType?'isCheck':'']"></view>
						<text class="checkName">{{item.label}}</text>
					</view>
				</view>
			</view>
			<view class="addInfo">
				<view class="infoItem">
					<view class="label">
						姓名
					</view>
					<input placeholder-style="font-family: 'PingFang SC'" class="uni-input" name="input"
						v-model="form.linkmanName" placeholder="与证件名称一致" />
				</view>
				<view class="infoItem">
					<view class="label">
						证件号码
					</view>
					<input placeholder-style="font-family: 'PingFang SC'" class="uni-input" name="input"
						v-model="form.linkmanCertificate" placeholder="请输入联系人证件号码" />
				</view>
				<view class="infoItem">
					<view class="label">
						手机号码
					</view>
					<input placeholder-style="font-family: 'PingFang SC'" class="uni-input" name="input"
						v-model="form.linkmanPhone" placeholder="请输入联系人手机号码" />
				</view>
				<view class="infoItem" v-if="form.linkmanCertificateType==1 || form.linkmanCertificateType==3">
					<view class="label">
						年龄
					</view>
					<input placeholder-style="font-family: 'PingFang SC'" class="uni-input" name="input"
						v-model="form.linkmanAge" placeholder="请输入联系人年龄" />
				</view>

			</view>
			<view class="user_agree">
				<view class="checkItem" @click="changeAgree">
					<view :class="['checkShow',agree?'isCheck':'']"></view>
					<text class="checkName">同意</text>
					<text class="user_agreement_btn" @click.stop="showUserAgreement(true)">《用户服务协议和隐私政策》</text>
				</view>
			</view>
		</view>
		<view class="sureChoose">
			<view class="upBtn" @click="sumbit">
				提交
			</view>
		</view>
		<view class="mask" v-show="isShowMask">
			<view class="maskContent">
				<view class="noticeTitle">用户服务协议和隐私政策</view>
				<view class="noticeView" v-for="(notice,index) in noticeList" :key="index">
					<view class="title">
						{{notice.title?notice.title:''}}
					</view>
					<view class="text" v-for="(text,index) in notice.textList" :key="index">
						{{text}}
					</view>
					<view class="item" v-for="(item,index) in notice.ulList" :key="index">
						{{item}}
					</view>
					<view class="foot">
						{{notice.footText?notice.footText:''}}
					</view>
				</view>
				<view :class="['agreeBtn']" @click="showUserAgreement(false)">
					已阅读
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		header
	} from '@/component/header.vue';
	import Utils from '@/utils/index.js'

	export default {
		data() {
			return {
				form: {
					linkmanAge: '',
					linkmanCertificate: '',
					linkmanCertificateType: 0,
					linkmanName: '',
					linkmanPhone: ''
				},
				cardTypeList: [{
					label: '身份证',
					value: 0
				}, {
					label: '港澳台居民通行证/居住证',
					value: 1
				}, {
					label: '临时身份证',
					value: 2
				}, {
					label: '护照',
					value: 3
				}],
				linkmanID: null,
				agree: false,
				isShowMask: false,

				noticeList: [{
						textList: [
							'我们非常重视对您的个人隐私保护，有时候我们需要某些信息才能为您提供您请求的服务，本隐私声明解释了这些情况下的数据收集和使用情况。'
						]
					},
					{
						title: '关于您的个人信息',
						textList: [
							'我们严格保护您个人信息的安全。我们使用各种安全技术和程序来保护您的个人信息不被未经授权的访问、使用或泄漏。',
							'我们会在法律要求或符合我们的相关服务条款、软件许可使用协议约定的情况下透露您的个人信息，或者有充分理由相信必须这样做才能：'
						],
						ulList: [
							'满足法律或行政法规的明文规定，或者符合我们APP/小程序适用的法律程序；',
							'符合我们相关服务条款、软件许可使用协议的约定；',
							'在紧急情况下保护服务的用户或大众的个人安全。'
						],
						footText: '我们不会未经您的允许将这些信息与第三方共享，本声明已经列出的上述情况除外。'
					},
					{
						title: '关于免责说明',
						textList: [
							'就下列相关事宜的发生，我们不承担任何法律责任：'
						],
						ulList: [
							'由于您将用户密码告知他人或与他人共享注册帐户，由此导致的任何个人信息的泄漏，或其他非因我们原因导致的个人信息的泄漏；',
							'我们根据法律规定或政府相关政策要求提供您的个人信息；',
							'任何由于黑客攻击、电脑病毒侵入或政府管制而造成的暂时性网站关闭；',
							'因不可抗力导致的任何后果；',
							'我们在各服务条款及声明中列明的使用方式或免责情形。'
						]
					}

				],

			};
		},
		components: {
			header,
		},
		onLoad(options) {
			this.linkmanID = options.id || null
			if (this.linkmanID) {
				this.$myRequest({
					url: '/auth/linkman/' + this.linkmanID,
					method: 'get'
				}).then((res) => {
					this.form = res.data.data
				})
			}
		},
		methods: {
			checkCardType(index) {
				this.form.linkmanCertificateType = index;
			},
			sumbit() {
				if(this.form.linkmanCertificateType == 3) {
					if (!Utils.checkMobile(this.form.linkmanPhone)) {
						uni.showModal({
							title: '提示',
							content: '请输入正确的手机号码',
							showCancel: false
						});
						return;
					}
				} else {
					let nameReg = /^[a-zA-Z\u4E00-\u9FA5\uf900-\ufa2d·s]{2,20}$/
					if (!(nameReg.test(this.form.linkmanName))) {
						uni.showModal({
							title: '提示',
							content: '请输入正确的姓名',
							showCancel: false
						});
						return;
					}
					let isLand = this.form.linkmanCertificateType == 0 || 2 // 0是内地, 1 是港澳台
					// if (!Utils.verifyIdCard(this.form.linkmanCertificate, isLand)) {
					// 	uni.showModal({
					// 		title: '提示',
					// 		content: '请输入正确的证件号码',
					// 		showCancel: false
					// 	});
					// 	return;
					// }
					if (!Utils.checkMobile(this.form.linkmanPhone)) {
						uni.showModal({
							title: '提示',
							content: '请输入正确的手机号码',
							showCancel: false
						});
						return;
					}
				}
				let ageReg = /^\d{1,2}$/
				if (this.form.linkmanCertificateType == 1 && !ageReg.test(this.form.linkmanAge)) {
					uni.showModal({
						title: '提示',
						content: '请输入年龄',
						showCancel: false
					});
					return;
				}

				if (!this.agree) {
					uni.showModal({
						title: '提示',
						content: '请详细阅读并勾选下方《用户服务协议和隐私政策》',
						showCancel: false
					});
					return;
				}

				if (this.linkmanID) {
					this.$myRequest({
						url: '/auth/linkman/edit',
						data: this.form,
						method: "put"
					}).then((res) => {
						uni.showToast({
							title: '修改成功',
							icon: 'success',
							duration: 2000,
							success: () => {
								uni.navigateBack();
							}
						})
					})
				} else {
					this.$myRequest({
						// url: '/auth/linkman/add',
						url: '/apitp/Userlinkman/add',
						data: this.form,
						method: "post"
					}).then((res) => {
						if (res.data.code === 200) {
							uni.showToast({
								title: '添加成功',
								icon: 'success',
								duration: 2000,
								success: () => {
									uni.navigateBack();
								}
							})
						}else{
							console.log('提交失败：', res.data.msg);
							return uni.showToast({
								title: res.data.msg,
								icon: 'none',
								duration: 2000,
								mask: true // ✅ 防止用户点击其他区域
							});
						}

					})
				}
			},

			changeAgree() {
				this.agree = !this.agree
			},
			showUserAgreement(flag) {
				this.isShowMask = flag
			}
		}
	}
</script>

<style lang="less" scoped>
	.addLink {
		width: 100%;
		height: auto;
		min-height: 100vh;
		font-family: 'PingFang SC';
		background-color: #f3f4f6;

		.addBox {
			width: 100%;
			height: auto;
			box-sizing: border-box;
			padding: 29upx;

			.label {
				min-width: 110upx;
				font-size: 27upx;
				color: #888888;
				font-weight: 600;
				// margin: 0 29upx;
				margin-right: 29upx;
			}

			.checkCardType {
				width: 100%;
				margin-bottom: 29upx;
				display: flex;
				align-items: center;
				background-color: #fff;
				padding: 0 15upx 0 29upx;
				box-sizing: border-box;
				border-radius: 15upx;

				.checkItem-itm {
					display: flex;
					flex-wrap: wrap;
					margin-bottom: 20upx;
					.checkItem {
						display: flex;
						align-items: center;
						margin-left: 20upx;
						margin-top: 20upx;
						.checkShow {
							width: 29upx;
							height: 29upx;
							line-height: 29upx;
							text-align: center;
							border: 2upx solid #888888;
							border-radius: 50%;
							font-weight: 700;
							margin-right: 10upx;
							font-size: 25upx;

							&.isCheck {
								background: #FFBA38;
								border: 2upx solid transparent;
								font-family: 'icongou';

								&::before {
									content: "\e66c";
									display: inline-block;
								}
							}
						}

						.checkName {
							color: #000000;
							font-size: 27upx;
							font-weight: 600;
						}

					}
				}
			}

			.addInfo {
				width: 100%;
				// height: 360upx;
				background-color: #fff;
				padding: 0 29upx;
				box-sizing: border-box;
				border-radius: 15upx;

				.infoItem {
					width: 100%;
					height: 92upx;
					display: flex;
					align-items: center;
					border-bottom: 1upx solid rgba(136, 136, 136, 0.5);

					&:last-of-type {
						border-bottom: 0;
					}

					.uni-input {
						color: #000000;
						font-size: 27upx;
						font-weight: 600;
						font-family: 'PingFang SC' !important;
					}
				}
			}
		}

		.user_agree {
			width: 100%;
			height: 150upx;
			padding: 20upx 29upx;
			box-sizing: border-box;

			.checkItem {
				display: flex;
				align-items: center;

				.checkShow {
					width: 29upx;
					height: 29upx;
					line-height: 29upx;
					text-align: center;
					border: 2upx solid #888888;
					border-radius: 50%;
					font-weight: 700;
					margin-right: 25upx;
					font-size: 25upx;

					&.isCheck {
						background: #FFBA38;
						border: 2upx solid transparent;
						font-family: 'icongou';

						&::before {
							content: "\e66c";
							display: inline-block;
						}
					}
				}

				.checkName {
					color: #000000;
					font-size: 27upx;
					font-weight: 600;
				}

				.user_agreement_btn {
					color: #5CB7FF;
					font-size: 27upx;
					font-weight: 600;
				}

			}

		}

		.sureChoose {
			width: 100%;
			height: 150upx;
			// background-color: #fff;
			// box-shadow: 20upx 10upx 20upx 10upx rgba(0, 0, 0, 0.4);
			position: fixed;
			bottom: 0;
			left: 0;
			display: flex;
			flex-direction: column;
			justify-content: space-evenly;
			align-items: center;
			box-sizing: border-box;
			padding: 0 29upx;

			.upBtn {
				width: 654upx;
				height: 77upx;
				text-align: center;
				line-height: 77upx;
				background: #5CB7FF;
				box-shadow: 0upx 6upx 12upx rgba(82, 162, 225, 0.34);
				border-radius: 10upx;
				font-size: 35upx;
				color: #fff;
			}
		}



		.mask {
			width: 100%;
			height: auto;
			background-color: rgba(0, 0, 0, 0.5);
			position: fixed;
			top: 0;
			bottom: 0;
			padding: 237upx 0;
			overflow: auto;
			z-index: 999;

			.maskContent {
				width: 654upx;
				height: auto;
				background: #FFFFFF;
				border: 2upx solid #707070;
				border-radius: 19upx;
				margin: 0 auto;
				box-sizing: border-box;
				padding: 58upx 58upx 48upx 58upx;
				// padding: 20upx;
				font-family: 'PingFang SC';

				.noticeTitle {
					color: #1B1B1B;
					font-size: 35upx;
					font-weight: bold;
					margin-bottom: 30upx;
					text-align: center;
				}

				.noticeView {
					width: 100%;
					height: auto;
					line-height: 50upx;
					text-align: justify;

					.title {
						font-size: 29upx;
						font-weight: 700;
					}

					.text {
						font-size: 27upx;
						margin: 30upx 0;
					}

					.item {
						padding-left: 40upx;
						position: relative;
						font-size: 27upx;
						margin-bottom: 20upx;
						line-height: 40upx;

						&::before {
							content: '';
							width: 10upx;
							height: 10upx;
							border-radius: 50%;
							background-color: #000;
							position: absolute;
							top: 0.5em;
							left: 0.5em;
						}
					}

					.foot {
						font-size: 27upx;
						margin: 40upx 0;
					}

				}

				.agreeBtn {
					width: 381upx;
					height: 77upx;
					text-align: center;
					line-height: 77upx;
					background: #5CB7FF;
					box-shadow: 0upx 6upx 12upx rgba(82, 162, 225, 0.34);
					border-radius: 10upx;
					font-size: 35upx;
					color: #fff;
					margin: 0 auto;

					&.gray {
						background-color: #888888;
						box-shadow: 0upx 6upx 12upx rgba(136, 136, 136, 0.34);
					}
				}
			}
		}

	}
</style>
