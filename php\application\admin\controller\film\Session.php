<?php

namespace app\admin\controller\film;

use app\common\controller\Backend;
use think\Db;

/**
 * 影片场次管理
 *
 * @icon fa fa-circle-o
 */
class Session extends Backend
{

    /**
     * Session模型对象
     * @var \app\admin\model\film\Session
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\film\Session;
    }

    /**
     * 获取指定年份和周的影片排期
     */
    public function schedule()
    {
        $year = $this->request->request('year');
        $week = $this->request->request('week');

        if (!$year || !$week) {
            $this->error("参数不完整");
        }

        $list = $this->model
            ->with(['film'])
            ->where('year', $year)
            ->where('week', $week)
            ->where('del_flag', 0)
            ->order('film_start_time asc ,film_arranged_date asc')
            ->select();

        // 分组处理
        $grouped = [];
        foreach ($list as $item) {
            $date = date('Y-m-d', strtotime($item['film_arranged_date']));
            if (!isset($grouped[$date])) {
                $grouped[$date] = [];
            }
            $grouped[$date][] = $item;
        }


        $this->success("获取成功", null, $grouped);
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");

            if (!$params) {
                $this->error(__('参数不能为空'));
            }


            // 1. 解析排期日期
            $date = new \DateTime($params['film_arranged_date']);
            $params['year'] = (int)$date->format("o"); // ISO 年（注意不同于 Y）
            $params['week'] = (int)$date->format("W"); // ISO 周
            $params['film_state'] = '01';

            $params['inventory_votes'] = $params['film_poll'];
            $params['del_flag'] = 0;
            $id = $this->model->validateFailException(true)->save($params);
            $this->success('添加成功', null, '添加成功');
        }

        $this->error('非法请求');
    }

    public function del($ids = "")
    {
        if ($this->request->isPost()) {
            if (!$ids) {
                $this->error("参数不能为空");
            }

            $pk = $this->model->getPk();
            $ids = is_array($ids) ? $ids : explode(',', $ids);

            // 逻辑删除：将 del_flag 设置为 2
            $count = $this->model
                ->where($pk, 'in', $ids)
                ->update(['del_flag' => 2]);

            if ($count > 0) {
                $this->success("删除成功");
            } else {
                $this->error("删除失败");
            }
        }

        $this->error("非法请求");
    }


    /**
     * 修改排期库存（加票/减票）
     * @return \think\response\Json
     */
    public function update_inventory()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        $params = $this->request->post();
        $sessionId = $params['session_id'] ?? null;
        $op = $params['op'] ?? 'none';
        $value = (int)($params['value'] ?? 0);

        if (!$sessionId || !in_array($op, ['inc', 'dec'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        if ($value <= 0) {
            return json(['code' => 0, 'msg' => '操作数量必须大于0']);
        }


        // 启用事务 + 行级锁（确保并发安全）
        Db::startTrans();
        try {
            $session = $this->model->where('id', $sessionId)->lock(true)->find();

            if (!$session) {
                throw new \Exception('排期不存在');
            }

            $currentPoll = (int)$session['film_poll'];
            $currentInventory = (int)$session['inventory_votes'];

            if ($op === 'inc') {
                // 增加库存与票数
                $session['film_poll'] = $currentPoll + $value;
                $session['inventory_votes'] = $currentInventory + $value;
            } elseif ($op === 'dec') {
                // 检查库存是否足够
                if ($currentInventory < $value) {
                    throw new \Exception('库存不足，无法减少');
                }
                $session['film_poll'] = $currentPoll - $value;
                $session['inventory_votes'] = $currentInventory - $value;
            }

            $session->save();

            Db::commit();
            return json(['code' => 1, 'msg' => '库存更新成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }
    public function add_whitelist()
    {
        $params = $this->request->post();

        // 参数校验
        // 参数校验
        $userPhone = $params['phone'] ?? '';
        $filmSessionId = $params['session_id'] ?? 0;
        $ticketType = (int)($params['count'] ?? 0);


        if (!isset($userPhone) || !isset($filmSessionId)) {
            return json(['code' => 0, 'msg' => '缺少必要参数']);
        }
        // 单独校验票数
        if (!is_numeric($ticketType) || intval($ticketType) != $ticketType || intval($ticketType) <= 0) {
            return json(['code' => 0, 'msg' => '票数错误']);
        }
        // 校验手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $userPhone)) {
            return json(['code' => 0, 'msg' => '手机号格式不正确']);
        }


        // 查询 user_id（模拟 selectUserCountByPhone）

        $Usermodel = new \app\admin\model\sys\User;
        $user = $Usermodel->where([
            'phonenumber' => $userPhone,
            // 'user_type' => '01',  //'i深圳：00   微信小程序：01',
            'del_flag' => '0'
        ])->value('user_id');

        if (!$user) {
            return json(['code' => 0, 'msg' => '手机号必须为注册用户']);
        }

        // 查询影片场次
        $filmSession = $this->model->where('id', $filmSessionId)->find();
        if (!$filmSession) {
            return json(['code' => 0, 'msg' => '影片不存在']);
        }

        if ($filmSession['film_state'] == '02') {
            return json(['code' => 0, 'msg' => '影片已下架']);
        }

        // 校验库存
        if ($filmSession['inventory_votes'] < $ticketType) {
            return json(['code' => 0, 'msg' => '库存不足']);
        }

        Db::startTrans();
        try {
            // 扣减库存
            $updateRes = $this->model
                ->where('id', $filmSessionId)
                ->update([
                    'inventory_votes' => $filmSession['inventory_votes'] - $ticketType
                ]);

            if (!$updateRes) {
                throw new \Exception('扣减库存失败');
            }

            // 生成批次号

            $batchNumber = $this->uuid();

            // 插入 film_subscribe 记录
            $subscribeList = [];
            for ($i = 0; $i < $ticketType; $i++) {
                $subscribeList[] = [
                    'film_seeion_id' => $filmSessionId,
                    'user_id' => $user,
                    'user_linkman_id' => 0,
                    'subscribe_state' => '1',
                    'subscribe_type' => $ticketType,
                    'batch_number' => $batchNumber,
                    'is_surplus' => '0'
                ];
            }
            $film_subscribe_model = new \app\common\model\film\Subscribe;

            $subRes = $film_subscribe_model->saveAll($subscribeList);
            if (!$subRes) {
                throw new \Exception('插入预约记录失败');
            }

            // 插入 white_list
            $whiteData = [
                'film_session_id' => $filmSessionId,
                'user_id' => $user,
                'user_phone' => $userPhone,
                'ticket_type' => $ticketType
            ];

            $whiteRes = Db::table('film_white_list')->insert($whiteData);
            if (!$whiteRes) {
                throw new \Exception('插入白名单失败');
            }

            Db::commit();
            return json(['code' => 1, 'msg' => '添加成功']);
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 0, 'msg' => '失败：' . $e->getMessage()]);
        }
    }

    public function sessionCopy()
    {
        $params = $this->request->post();

        $sourceYear = isset($params['source_year']) ? intval($params['source_year']) : 0;
        $sourceWeek = isset($params['source_week']) ? intval($params['source_week']) : 0;
        $targetYear = isset($params['target_year']) ? intval($params['target_year']) : 0;
        $targetWeek = isset($params['target_week']) ? intval($params['target_week']) : 0;

        if (!$sourceYear || !$sourceWeek || !$targetYear || !$targetWeek) {
            return json(['code' => 0, 'msg' => '缺少参数']);
        }


        // 查询来源周数据
        $sourceSessions =  $this->model
            ->where([
                'year' => $sourceYear,
                'week' => $sourceWeek,
                'del_flag' => '0'
            ])
            ->select();

        if (!$sourceSessions) {
            return json(['code' => 0, 'msg' => '排片日期无数据，无法拷贝']);
        }

        // 检查目标周是否存在已预约数据（即票数不一致）
        $conflict = $this->model
            ->where(['year' => $targetYear, 'week' => $targetWeek])
            ->whereRaw('film_poll != inventory_votes')
            ->count();

        if ($conflict > 0) {
            return json(['code' => 0, 'msg' => '已存在预约记录，不能覆盖']);
        }
        // 删除目标周数据
        $this->model
            ->where(['year' => $targetYear, 'week' => $targetWeek])
            ->delete();

        $insertData = [];
        $weekDiff = $targetWeek - $sourceWeek;
        $weekOffsetStr = "{$weekDiff} week";

        foreach ($sourceSessions as $session) {
            // 获取原始安排日期的周几（1=周一，7=周日）
            $dayOfWeek = date('N', strtotime($session['film_arranged_date']));

            // 重新构建目标日期，修正DateTime命名空间问题
            $targetArrangedDate = (new \DateTime())->setISODate($targetYear, $targetWeek, $dayOfWeek)->format('Y-m-d');

            // 取出当天时间部分
            $startTime = date('H:i:s', strtotime($session['film_start_time']));
            $endTime   = date('H:i:s', strtotime($session['film_end_time']));

            $insertData[] = [
                'film_id'           => $session['film_id'],
                'film_poll'         => $session['film_poll'],
                'inventory_votes'   => $session['film_poll'], // 重置库存
                'film_state'        => '01',
                'film_arranged_date' => $targetArrangedDate,
                'film_start_time'   => $targetArrangedDate . ' ' . $startTime,
                'film_end_time'     => $targetArrangedDate . ' ' . $endTime,
                'week'              => $targetWeek,
                'year'              => $targetYear,
                'del_flag'          => '0',
                'create_time'       => date('Y-m-d H:i:s'),
            ];
        }


        // 批量插入
        $this->model->saveAll($insertData);
        $this->success("拷贝成功");
    }


    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    /**
     * 生成 UUID v4 字符串（如 e9134e1f-6b7a-4553-b041-884a02b3f239）
     * @return string
     */
    public static function uuid()
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }
}
