<template>
	<!-- 观影预约成功 -->
	<view class="scheme_film_success">
		<Header :isFixed="true" menuClass="bor" :isBack="true" :isShowHome="true" title="预约成功" :background="`#fff`"
			:color="'#000'">
		</Header>
		<Header :background="`transparent`">
		</Header>
		<view class="scheme">
			<view class="scheme_state">
				<view class="scheme_state_head">
					<image src="../../static/img/schemesuccess/schemestate_course_film.png" mode="aspectFill"></image>
					<view>预约成功</view>
				</view>
				<view class="scheme_state_tips">
					<view class="scheme_state_tips_title">
						注意:
					</view>
					<view class="scheme_state_tips_text" v-for="(childItem,childIndex) in tipstext" :key="childIndex">
						{{childItem}}
					</view>
					<!-- <view class="scheme_state_tips_text">
						2.请提前
						<text>10分钟签到获取二维码</text>
						，否则该预约失效，需重新预约
					</view>
					<view class="scheme_state_tips_text">
						3.获取二维码后，进影院观影截止时间为
						<text>电影开场时间</text>
					</view> -->
				</view>
			</view>

			<view class="scheme_info">
				<view class="scheme_title">
					宝安科技馆 观影预约
				</view>
				<view class="scheme_film_info">
					<view class="scheme_film_cover">
						<image :src="filmInfo.filmCover" mode="aspectFill">
						</image>
					</view>
					<view class="scheme_film_text">
						<view class="itemLeft">
							<view class="scheme_film_name">
								{{filmInfo.filmName}}
							</view>
							<view class="scheme_film_Type">
								类型：{{filmTypeList[filmInfo.filmType-1]}}
							</view>
							<view class="scheme_film_date">
								时间：{{filmInfo.filmStartTime}}-{{filmInfo.filmEndTime}}
							</view>
							<view class="scheme_film_day">
								日期：{{filmInfo.filmArrangedDate}}
								{{weekList[new Date(filmInfo.filmArrangedDate).getDay()]}}
							</view>
						</view>
						<view class="itemRight">
							<view class="orderNum">已预约:{{filmInfo.orderNum}}人</view>
						</view>
					</view>
				</view>
				<view class="tips-view">
					<view>
						<icon type="warn" size="14" class="warn-icon"></icon>
						<text class="tips-text">{{tipstitle[0]}}</text>
					</view>
					<view class="tips-text-bottom-view">
						<text class="tips-text">{{tipstitle[1]}}</text>
					</view>
				</view>
				<view class="scheme_film_qrcode">
					<view :class="['surplus_sign_in',isShowCountDown?'':'hidden']">
						<view class="sign_in_text">
							剩余
							<text v-if="filmInfo.subscribeState==4">电影进场</text>
							<text v-else>签到</text>
							时间
						</view>
						<view class="sign_in_time">
							{{countDown}}
						</view>
					</view>
					<view class="sign_in_qrcode">
						<!-- <image :class="[qrcodeList[qrcodeIndex].flag==0?'qrcodeMask':'']" :src="qrcodeList[qrcodeIndex].qrcodeUrl" v-if="qrcodeList.length>0" @click="getQrcode"> -->
						<image :class="['qrcodeMask']" :src="qrcodeList[qrcodeIndex].qrcodeUrl"
							v-if="qrcodeList.length>0" @click="getQrcode">
						</image>
						<image src="../../static/img/schemesuccess/qrcode.png" mode="" v-else></image>

						<!-- 电影因人数不足取消放映 -->
						<view class="qrcode_mask" v-if="filmInfo.subscribeState==1 || !showQrcode || !isPlayFilm">
							<view :class="[delayShowFlag ? '':'button-wrapper']">
								<button type="success" class="cancel-infor-btn"
									v-if="!isPlayFilm&&(filmInfo.subscribeState==4 || filmInfo.subscribeState==1)">电影因人数不足取消放映</button>
								<button class="maskBtn" type="success"
									v-else-if="!showQrcode&&filmInfo.subscribeState==4">
									<view>影片未开始</view>
									<view>提前十分钟入场</view>
								</button>
								<button type="success" v-else @click="signUp(filmInfo)">签到获取二维码</button>
							</view>
						</view>

						<!-- 核销情况 -->
						<view class="qrcode_mask_expired"
							v-show="qrcodeList[qrcodeIndex].flag==0 || (isMiss && filmInfo.subscribeState==4)">
							<button type="success" v-if="qrcodeList[qrcodeIndex].flag==0">已核销</button>
							<button type="success" v-else>核销时间已过</button>
						</view>
					</view>
					<view class="check_qrcode_btns">
						<button :class="['check_qrcode',qrcodeIndex===index?'active':'']"
							v-for="(qrcode,index) in qrcodeList" :key="qrcode.param" @click="changeQrcodeIndex(index)">
							{{qrcodeBtns[index]}}
						</button>
					</view>
					<view class="qrcode_tips">
						请提前签到并凭借二维码在电影开场前进场，多次失约将进入黑名单
					</view>
				</view>
				<view class="scheme_info_border">

				</view>
				<view class="scheme_tips">
					在首页
					<text>个人中心—观影预约—查看凭证</text>
					中查看此凭证，提前10分钟签到获取二维码，扫码进场
				</view>
			</view>
		</view>
		<view class="mask" v-show="isShowMaskLocation">
			<view class="blackDefault">
				<view class="locationStateIcon"></view>
				<view class="blackStateT">
					<text>您的定位较远</text>
					<br />
					<text>请移步至宝安科技馆进行<text class="blackDate">现场签到</text></text>
				</view>
				<view class="blackBtn" @click="isShowMaskLocation=false">返回</view>
			</view>
		</view>
	</view>
</template>

<script>
	import Header from '@/component/header.vue';
	import drawQrcode from "@/common/js/wxqrcode.js";
	import Utils from '@/utils/index.js'
	export default {
		data() {
			return {
				filmInfo: {},
				filmSessionId: null,
				batchNumber: null,
				qrcodeList: [],
				qrcodeIndex: 0,
				qrcodeBtns: ['票码一', '票码二', '票码三'],

				filmTypeList: ['球幕电影', '4D电影'],
				weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
				isShowSignCountDown: false,
				isShowCountDown: false, // 是否显示倒计时, 判断当前时间是否是开始当天
				countDown: null, // 倒计时
				countDownTimer: null, // 倒计时定时器
				// 截止时间
				endTime: null,
				// 当前时间
				nowTime: null,
				isShowMaskLocation: false,
				isShowQuickClick: false,
				timer: null, // 二维码定时器
				showQrcode: false, // 是否显示二维码
				filmStartTime: null,
				tipstext: [],
				tipstitle: [],
				isMiss: false,
				filmInforTimer: null,
				isPlayFilm: true,
				delayShowFlag: false
			}
		},
		components: {
			Header,
		},
		// created() {
		// 	// this.qrcodeList.forEach((item, index) => {
		// 	// 	let qrcodeUrl = QR.createQrCodeImg(`${item.param}`, {
		// 	// 		size: parseInt(300), //二维码大小
		// 	// 	})
		// 	// 	item.qrcodeUrl = qrcodeUrl
		// 	// })
		// },
		onLoad(options) {
			console.log('options = ',options)
			//batchNumber=9947c8df-d781-4bed-b806-709697067255&filmSessionId=6555
			this.batchNumber = options.batchNumber
			this.filmSessionId = options.filmSessionId
			//判断 用户是否有场馆预约
			this.subscribeEstimate()			
		},
		onShow() {
			//获取须知
			this.getlist()
			//获取影片场次详细信息
			this.$myRequest({
				// url: '/web/fileSession/personalCenterFilmByFilmSessionId',
				url: '/apitp/Filmsession/personalCenterFilmByFilmSessionId',
				data: {
					filmSessionId: this.filmSessionId
				},
				method: 'get'
			}).then((res) => {
				this.filmInfo = res.data.data
			console.log('this.filmInfo = ',this.filmInfo)
				this.filmStartTime = this.filmInfo.filmStartTime
				this.filmInfo.filmStartTime = Utils.changeTime(this.filmInfo.filmStartTime.replace(/-/g, '/'))
				this.filmInfo.filmEndTime = Utils.changeTime(this.filmInfo.filmEndTime.replace(/-/g, '/'))
				//预约人数
				this.filmInfo.orderNum = this.filmInfo.filmPoll - this.filmInfo.inventoryVotes
				console.log(this.filmInfo);
			
				this.$myRequest({
					// url: '/web/fileSession/isCheck',
					url: '/apitp/Filmsession/isCheck',
					data: {
						batchNumber: this.batchNumber
					},
					method: 'get'
				}).then((res) => {
					this.filmInfo.subscribeState = res.data.data
					if (this.filmInfo.subscribeState == 1) {
						// uni.showModal({
						// 	title: '注意',
						// 	content: '1、凭借入馆预约进馆，方可观影\r\n2、请提前30分钟签到获取二维码',
						// 	cancelText: '入馆预约',
						// 	confirmText: '确定',
						// 	success: (res) => {
						// 		if (res.cancel) {
						// 			uni.navigateTo({
						// 				url: '/pages/entervenue/index'
						// 			})
						// 		} else if (res.confirm) {
			
						// 		}
						// 	}
						// })
					}
			
					if (this.filmInfo.subscribeState == 4) {
						this.getQrcode()
					}
				})
				// this.repeatFilmInfor(this.filmSessionId);
				this.getCountDown()
			})
	
			// this.repeatFilmInfor(this.filmSessionId);
		},
		onHide() {
			clearInterval(this.countDownTimer)
			clearTimeout(this.timer)
			if (this.filmInforTimer) {
				clearTimeout(this.filmInforTimer)
			}
		},
		onUnload() {
			clearInterval(this.countDownTimer)
			clearTimeout(this.timer)
			if (this.filmInforTimer) {
				clearTimeout(this.filmInforTimer)
			}
		},
		methods: {
			getlist() {
				this.$myRequest({
					// url: '/admin/entrance_announcement/' + 3,
					url: "/apitp/Announcement/getEntranceInfo",
        			data: {id: 3},
					method: 'get',
				}).then(res => {
					if (res.data.code === 200) {
						let ent = res.data.data
						let audit = ent.beiyongThree.split("\n")
						let object = ent.beiyongFour.split("\n")
						// let register = ent.entranceRegister.split("\n")
						this.tipstext = audit
						this.tipstitle = object
					}
				})
			},
			changeQrcodeIndex(index) {
				this.qrcodeIndex = index
			},
			getCountDown() {
				this.isMiss = false;
				clearInterval(this.countDownTimer)
				//距离开场时间10分钟
				let showQrcodeTime = new Date(this.filmStartTime).getTime() - 1000 * 60 * 10
				//距离开场时间10分钟
				let checkPeopleTime = new Date(this.filmStartTime).getTime() - 1000 * 60 * 10
				this.nowTime = new Date().getTime()
				let time = this.endTime - this.nowTime + 1609430400000

				//是否显示倒计时			
				this.isShowCountDown = Utils.changeTime(new Date().toISOString(), true) == Utils.changeTime(this.filmInfo.filmArrangedDate, true)
				this.countDownTimer = setInterval(() => {
					let now = new Date().getTime()

					//开场10分钟前显示二维码
					if (now >= showQrcodeTime) {
						this.showQrcode = true
					}
					//开场10分钟前检查观影人数
					if (now >= checkPeopleTime) {
						if (this.filmInfo) {
							let tempNum = this.filmInfo.filmType == 1 ? 8 : 5;
							if (this.filmInfo.hasOwnProperty('orderNum')) {
								if (this.filmInfo.orderNum < tempNum) {
									this.isPlayFilm = false;
								}
							}
							
						}
					}
					if (time > 1609430400000 + 1000) {
						time -= 1000
						// this.countDown = Utils.changeTime(time, false)
						this.countDown = Utils.formatTime(time, false)
					} else {
						if (!this.countDown) {
							this.countDown = '00:00:00'
						}
						this.isMiss = true;
					}

					this.delayShowFlag = true;
				}, 1000)
			},
			date2MS(date) {
				let date1 = new Date(date)
				let m = date1.getMinutes() < 10 ? '0' + date1.getMinutes() : date1.getMinutes()
				let s = date1.getSeconds() < 10 ? '0' + date1.getSeconds() : date1.getSeconds()
				return m + ':' + s
			},
			// 1.判断从当前时刻-未来的时间，是否有场馆预约，有才可以进行影视预约
			subscribeEstimate() {
				this.$myRequest({
					// url: '/web/fileSession/subscribeEstimate',
					url: '/apitp/Filmsession/subscribeEstimate',
					method: 'get'
				}).then((res) => {
					if (res.data.data[0].signState == 0) {
						uni.showModal({
							title: '注意',
							content: '1、凭借入馆签到进馆，方可观影\r\n2、请提前10分钟签到获取二维码',
							showCancel: false,
							confirmText: '入馆签到',
							success: function(res) {
								if (res.confirm) {
									uni.navigateTo({
										// url: '/pages/user/venuescheme'
										url: '/pages_app/user/venuescheme'
									});
								}
							}
						})
						return
					}
				})
			},
			// 点击签到
			signUp(filmInfo) {
				this.$myRequest({
					url: '/web/fileSession/subscribeEstimate',
					method: 'get'
				}).then((res) => {
					let showQrcodeTimes = new Date(this.filmStartTime).getTime() - 1000 * 60 * 10
					let now = new Date().getTime()

					if (res.data.data[0].signState == 0 && now < showQrcodeTimes) {
						uni.showModal({
							title: '注意',
							content: '1、凭借入馆签到进馆，方可观影\r\n2、请提前10分钟签到获取二维码',
							showCancel: false,
							confirmText: '入馆签到',
							success: function(res) {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages/user/venuescheme'
									});
								}
							}
						})
					} else {
						if (!this.isShowCountDown) {
							uni.showModal({
								title: '提示',
								content: '签到时间为影片开始当天',
								showCancel: false,
							})
							return;
						}
						if (Utils.timeThenNow(new Date(this.filmInfo.filmStartTime).getTime() + 1000 * 60 * 25)) {
							uni.showModal({
								title: '提示',
								content: '签到时间已过',
								showCancel: false,
							})
							return;
						}
						if (this.isShowQuickClick) {
							uni.showToast({
								title: '操作过快!!!',
								icon: 'error'
							})
							return;
						}

						uni.showLoading({
							title: '获取位置信息...',
							mask: true
						})
						const baoanLocation = this.$baoanLocation
						uni.getLocation({
							type: 'gcj02',
							isHighAccuracy: true,
							success: (res) => {
								console.log(res);
								if (Utils.distance(res.latitude, res.longitude, baoanLocation.la,
										baoanLocation.lo) >
									0.5) {
									this.isShowMaskLocation = true;
								} else {
									// 签到
									this.$myRequest({
										url: '/web/fileSession/check',
										data: {
											batchNumber: this.batchNumber
										}
									}).then((res) => {
										this.filmInfo.subscribeState = '4'
										this.getQrcode()
									})
								}
							},
							fail: (res) => {
								console.log(res);
								this.isShowQuickClick = true
								setTimeout(() => {
									this.isShowQuickClick = false
								}, 10000)
								wx.getSetting({
									success: res => {
										if (typeof(res.authSetting[
												'scope.userLocation']) !=
											'undefined' && !res.authSetting[
												'scope.userLocation']) {
											// 用户拒绝了授权
											wx.showModal({
												title: '提示',
												content: '您拒绝了定位权限，将无法使用签到功能，点击确定去开启定位权限',
												success: res => {
													if (res.confirm) {
														// 跳转设置页面
														wx.openSetting({
															success: res => {
																if (res
																	.authSetting[
																		'scope.userLocation'
																	]
																) {
																	// 授权成功，重新定位
																	wx.getLocation({
																		success: res => {
																			if (Utils
																				.distance(
																					res
																					.latitude,
																					res
																					.longitude,
																					baoanLocation
																					.la,
																					baoanLocation
																					.lo
																				) >
																				0.5
																			) {
																				this.isShowMaskLocation =
																					true;
																			} else {
																				// 签到
																				this.$myRequest({
																						url: '/web/fileSession/check',
																						data: {
																							batchNumber: this
																								.batchNumber
																						}
																					})
																					.then(
																						(
																							res
																							) => {
																							this.filmInfo
																								.subscribeState =
																								'4'
																							this.getQrcode()
																						}
																					)
																			}
																		}
																	});
																} else {
																	// 没有允许定位权限
																	wx.showToast({
																		title: '您拒绝了定位权限，将无法使用签到功能',
																		icon: 'none'
																	});
																}
															}
														});
													}
												}
											});
										}
									}
								});
							},
							complete: () => {
								wx.hideLoading()
							}
						})

						// 签到
						// this.$myRequest({
						// 	url: '/web/fileSession/check',
						// 	data: {
						// 		batchNumber: this.batchNumber
						// 	}
						// }).then((res) => {
						// 	this.filmInfo.subscribeState = '4'
						// 	this.getQrcode()
						// })
					}
				})
			},
			getQrcode() {
				clearTimeout(this.timer)
				// if(!this.showQrcode && this.filmInfo.subscribeState==4) return;
				this.$myRequest({
					url: '/web/fileSession/qrCodeInfo',
					data: {
						batchNumber: this.batchNumber,
						filmType: this.filmInfo.filmType
					},
					method: 'get'
				}).then((res) => {
					let nowTime = new Date().getTime()
					let time = res.data.data.length && res.data.data[0].time
					if (!time) {
						clearTimeout(this.timer)
						uni.showModal({
							title: '提示',
							content: '状态已更改, 返回上一页',
							showCancel: false,
							success(res) {
								if (res.confirm) {
									uni.navigateBack()
								}
							}
						})
					}
					this.timer = setTimeout(() => {
						this.getQrcode()
					}, 1000 * 30)
					this.qrcodeList = res.data.data.map((item) => {
						return {
							...item,
							qrcodeUrl: QR.createQrCodeImg(
								`${item.param+'&filmType='+this.filmInfo.filmType}`, {
									size: parseInt(100), //二维码大小
								})
						}
					})
				})
			},
			repeatFilmInfor(filmSessionId) {
				let self = this;
				this.$myRequest({
						// url: '/web/fileSession/personalCenterFilmByFilmSessionId',
						url: '/apitp/Filmsession/personalCenterFilmByFilmSessionId',
						data: {
							filmSessionId: filmSessionId
						},
						method: 'get',
						noLoadingFlag: true
					})
					.then((res) => {
						if (!this.filmInfo) {
							this.filmInfo = res.data.data
							this.filmStartTime = this.filmInfo.filmStartTime
							this.filmInfo.filmStartTime = Utils.changeTime(this.filmInfo.filmStartTime.replace(/-/g,
								'/'))
							this.filmInfo.filmEndTime = Utils.changeTime(this.filmInfo.filmEndTime.replace(/-/g, '/'))
						}
						//预约人数
						this.$set(this.filmInfo, 'orderNum', this.filmInfo.filmPoll - this.filmInfo.inventoryVotes);

						// console.log('======', this.filmInfo.orderNum)
						self.filmInforTimer = setTimeout(function() {
							self.repeatFilmInfor(filmSessionId);
						}, 30000)
					})
			}
		},
		watch1: {
			'filmInfo.subscribeState': {
				handler(subscribeState) {
					let filmArrangedDate = this.filmInfo.filmArrangedDate ? this.filmInfo.filmArrangedDate.replace(/-/g,
						'/') : ''
					// 影片开始时间 - 10分钟 = 截止签到时间
					this.endTime = new Date(filmArrangedDate + " " + this.filmInfo.filmStartTime).getTime() - 1000 *
						60 * 10
					if (this.filmInfo.isSurplus == 1) {
						this.endTime = new Date(filmArrangedDate + " " + this.filmInfo.filmStartTime).getTime() -
							1000 * 60 * 10
					}
					if (subscribeState == 4) {
						// this.endTime = new Date(filmArrangedDate + " " + this.filmInfo.filmStartTime).getTime() +
						// 	1000 * 60 * 5
						this.endTime = new Date(filmArrangedDate + " " + this.filmInfo.filmStartTime).getTime()
					}
					this.getCountDown()
				},
				immediate: true
			},
		}
	}
</script>

<style lang="less" scoped>
	.scheme_film_success {
		width: 100%;
		height: 100vh;
		overflow: scroll;
		background: #F3F4F6;

		.scheme {
			padding: 28upx 28upx 130upx;

			.scheme_state {
				background-color: #fff;
				padding: 28upx 20upx;
				border-radius: 10rpx;

				&>view {
					padding-top: 40upx;
					box-sizing: border-box;
				}

				.scheme_state_head {
					width: 208upx;
					// height: 228upx;
					margin: 0 auto;
					text-align: center;
					// background-color: red;
					font-size: 0;
					padding-top: 20upx;
					// display: flex;
					// flex-direction: column;
					// justify-content: space-between;

					&>image {
						width: 208upx;
						height: 142upx;
						margin-bottom: 38upx;
					}

					&>view {
						// margin-top: 38upx;
						font-size: 34upx;
						font-family: "PingFang SC";
						font-weight: 600;
						color: #5CB7FF;
						letter-spacing: 6upx
					}
				}

				.scheme_state_tips {

					font-size: 24upx;
					font-family: "PingFang SC";
					font-weight: 400;
					color: #888888;

					.scheme_state_tips_title {
						font-weight: 500;
						color: #000;
					}

					.scheme_state_tips_text {
						text {
							color: #FFBA38
						}
					}
				}
			}

			.scheme_info {
				margin-top: 28upx;
				padding: 20upx;
				border-radius: 10upx;
				background-color: #fff;

				.scheme_title {
					font-size: 34upx;
					font-family: "PingFang SC";
					font-weight: 600;
					color: #000000;
				}

				.scheme_film_info {
					display: flex;
					padding: 20upx;
					margin-top: 20upx;

					background: #F3F4F6;
					border-radius: 10upx;

					.scheme_film_cover {
						width: 144upx;
						height: 192upx;
						border-radius: 10upx;

						image {
							width: 100%;
							height: 100%;
							border-radius: 20upx;
						}
					}

					.scheme_film_text {
						flex: 1;
						margin-left: 28upx;
						font-size: 24upx;
						font-family: "PingFang SC";
						font-weight: 400;
						color: #888888;

						display: flex;
						align-items: center;

						.itemLeft {
							flex: 1;

							.scheme_film_name {
								font-size: 28upx;
								font-family: "PingFang SC";
								font-weight: 600;
								color: #000000;
							}
						}

						.itemRight {
							.orderNum {
								height: 58upx;
								padding: 0 10upx;
								line-height: 58upx;
								text-align: center;
								color: #fff;
								background: linear-gradient(180deg, #FFCA5F 0%, #FFB33C 100%);
								box-shadow: 0upx 6upx 12upx rgba(255, 154, 54, 0.34);
								border-radius: 38upx;
							}
						}
					}
				}

				.tips-view {
					position: relative;
					padding: 20upx;
					margin-top: 15upx;
					line-height: 30rpx;
					text-align: center;
					background: #F3F4F6;
					border-radius: 10upx;

					.warn-icon {
						margin-right: 10upx;
						vertical-align: top;
					}

					.tips-text {
						font-size: 26upx;
						font-family: "PingFang SC";
						vertical-align: top;
					}

					.tips-text-bottom-view {
						text-indent: 35upx;
					}
				}

				.scheme_film_qrcode {
					padding: 0 58upx;

					.surplus_sign_in {
						text-align: center;
						font-size: 26upx;
						color: #000;
						font-family: "PingFang SC";
						margin-top: 58upx;
						margin-bottom: 28upx;

						&.hidden {
							visibility: hidden;
						}

						.sign_in_text {
							text {
								color: #FFBA38;
							}
						}

						.sign_in_time {

							font-size: 42upx;
							font-weight: 600;
							color: #FFBA38;
						}
					}

					.sign_in_qrcode {
						width: 366upx;
						height: 366upx;
						text-align: center;
						position: relative;
						font-size: 0;
						margin: 0 auto 48upx;

						image {
							width: 100%;
							height: 100%;
						}

						.qrcode_mask_expired {
							width: 442upx;
							height: 366upx;
							background-image: url(../../static/img/schemesuccess/qrcode_mask_expired.png);
							background-size: 100% 100%;
							position: absolute;
							left: -38upx;
							top: 0;
							background-color: #fff;

							button {
								width: 346upx;
								height: 76upx;
								background: #888;
								box-shadow: 0upx 6upx 12upx rgba(136, 136, 136, 0.34);
								border-radius: 10upx;

								font-size: 34upx;
								font-family: "PingFang SC";
								font-weight: 400;
								color: #FFFFFF;

								position: absolute;
								left: 50%;
								top: 50%;
								transform: translate(-50%, -50%);

								&::after {
									border: none;
								}
							}
						}

						.qrcode_mask {
							width: 368upx;
							height: 368upx;
							background-color: rgba(255, 255, 255, .9);
							position: absolute;
							left: 50%;
							top: 0;
							transform: translateX(-50%);

							.button-wrapper {
								display: none;
							}

							button {
								width: 346upx;
								height: 76upx;
								background: #5CB7FF;
								box-shadow: 0upx 6upx 12upx rgba(82, 162, 225, 0.34);
								border-radius: 10upx;

								font-size: 34upx;
								font-family: "PingFang SC";
								font-weight: 400;
								color: #FFFFFF;

								position: absolute;
								left: 50%;
								top: 50%;
								transform: translate(-50%, -50%);

								&::after {
									border: none;
								}

								&.cancel-infor-btn {
									font-size: 26upx;
									height: 70upx;
								}
							}

							.maskBtn {
								height: 100upx;
								line-height: 50upx;
							}
						}
					}

					.check_qrcode_btns {
						display: flex;
						justify-content: space-between;
						margin-bottom: 58upx;

						.check_qrcode {
							width: 154upx;
							height: 58upx;
							line-height: 58upx;
							text-align: center;
							background: #F3F4F6;
							border-radius: 10upx;
							font-size: 26upx;
							font-family: "PingFang SC";
							font-weight: 400;
							color: #888888;
							box-shadow: 0upx 6upx 12upx rgba(82, 162, 225, 0.34);

							&::after {
								border: none;
							}

							&.active {
								background: #5CB7FF;
								color: #FFFFFF;
							}
						}
					}

					.qrcode_tips {
						margin: 0 auto;
						width: 432upx;
						font-size: 24upx;
						font-family: "PingFang SC";
						font-weight: 400;
						color: #888888;
						text-align: center;
						margin-bottom: 28upx;
					}
				}

				.scheme_info_border {
					width: 100%;
					height: 2upx;
					background-color: rgba(136, 136, 136, 0.5);

				}

				.scheme_tips {
					margin-top: 28upx;
					font-size: 24upx;
					font-family: "PingFang SC";
					font-weight: 400;
					color: #888888;

					text {
						color: #FFBA38
					}
				}
			}
		}

		.mask {
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.5);
			position: fixed;
			left: 0;
			top: 0;

			.blackDefault {
				width: 615upx;
				height: 500upx;
				background: #FFFFFF;
				border-radius: 38upx;
				position: absolute;
				box-sizing: border-box;
				padding-top: 210upx;
				// margin: 794upx auto 0 auto;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);

				.locationStateIcon {
					width: 304upx;
					height: 304upx;
					position: absolute;
					top: 0;
					left: 0;
					transform: translate(50%, -50%);
					background: url(../../static/img/ordersate/sign_fail.png) no-repeat center center;
					background-size: 100% 100%;
				}

				.blackStateT {
					text-align: center;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					font-size: 28upx;
					font-family: "PingFang SC";
					font-weight: 400;
					color: #888;

					.blackDate {
						color: #FE8315;
					}
				}

				.blackBtn {
					width: 381upx;
					height: 77upx;
					line-height: 77upx;
					text-align: center;
					border-radius: 10upx;
					margin: 0 auto;

					font-size: 34upx;
					font-family: PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					position: absolute;
					bottom: 78upx;
					left: 50%;
					transform: translateX(-50%);


					background: linear-gradient(180deg, #F2AF30 0%, #FF8315 100%);
					box-shadow: 0upx 6upx 12upx rgba(245, 189, 84, 0.34);
				}
			}
		}
	}
</style>
