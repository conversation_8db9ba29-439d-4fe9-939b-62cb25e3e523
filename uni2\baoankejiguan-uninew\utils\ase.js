import CryptoJS from "crypto-js";

//字符串加密方法  	*注意：以下key和iv 前后台保持一致
function encrypt(data) {
	var iv = CryptoJS.enc.Latin1.parse('PLMoknijb$456lnb'); //16位的偏移量
	var key = CryptoJS.enc.Latin1.parse('AKApoiureqasdfg@'); //16位的密钥
	return CryptoJS.AES.encrypt(data, key, {
		iv: iv,
		mode: CryptoJS.mode.CBC,
		padding: CryptoJS.pad.ZeroPadding
	}).toString();
}

//字符串解密方法
function decrypt(data) {
	var iv = CryptoJS.enc.Latin1.parse('PLMoknijb$456lnb'); //16位的偏移量
	var key = CryptoJS.enc.Latin1.parse('AKApoiureqasdfg@'); //16位的密钥
	var decrypted = CryptoJS.AES.decrypt(data, key, {
		iv: iv,
		mode: CryptoJS.mode.CBC,
		padding: CryptoJS.pad.Pkcs7
	});
	return decrypted.toString(CryptoJS.enc.Utf8);
}

module.exports = {
	decrypt: decrypt,
	encrypt: encrypt
}
