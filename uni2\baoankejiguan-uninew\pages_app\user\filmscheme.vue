<template>
	<view class="film_scheme">
		<Header menuClass="bor" :isBack="true" :isShowHome="true" title="观影预约" color="#000" :background="`#fff`">
		</Header>
		<scroll-view class="film_scheme_list" scroll-y @scrolltolower="pageChange()">
			<view class="scheme" :style="{
				paddingBottom : menuButtonInfo.height + menuButtonInfo.top + 38 + 'px'
			}">
				<view class="file_item" v-for="(filmSession,index) in filmSessionList" :key="filmSession.filmSessionId">
					<view class="scheme_item_title">
						宝安科技馆 观影预约
					</view>
					<view class="file_head">
						<view class="file_head_left">
							<image :src="filmSession.filmCover" mode="aspectFill" :alt="filmSession.filmName" />
						</view>
						<view class="file_head_right">
							<view class="file_name">{{filmSession.filmName}}</view>
							<view class="file_type">
								类型：{{filmTypeList[filmSession.filmType - 1]}}
							</view>
							<view class="file_time">
								时间：{{filmSession.filmStartTime}} - {{filmSession.filmEndTime}}
							</view>
							<view class="file_time">
								日期：{{filmSession.filmArrangedDate}}
								{{weekList[new Date(filmSession.filmArrangedDate).getDay()]}}
							</view>
						</view>
					</view>
					<view class="file_count">
						预约票数：
						<text>{{filmSession.subscribeType}}张</text>
					</view>
					<view class="file_btn" v-if="filmSession.subscribeState==1||filmSession.subscribeState==4">
						<view class="cancel_scheme" :data-filmSessionId="filmSession.filmSeeionId"
							:data-batchNumber="filmSession.batchNumber" :data-vote="filmSession.subscribeType"
							@click="cancelScheme">
							取消预约
						</view>
						<view class="qrcode_check" :data-batchnumber="filmSession.batchNumber" :data-filmseeionid="filmSession.filmSeeionId"  @click="toFilmQrcode">
							<span v-if="filmSession.subscribeState == 4">
								{{filmSession.isTicket == 0 ? '去检票': '已检票'}}
							</span>
							<span v-else>{{btntextList[filmSession.subscribeState-1]}}</span>
						</view>
					</view>
					<view class="file_cancel_btn" v-else>
						<view class="cancel_btn">
							{{btntextList[filmSession.subscribeState-1]}}
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		header
	} from '@/component/header.vue';
	import Utils from '@/utils/index.js'
	export default {
		data() {
			return {
				filmSessionList: [],
				filmTypeList: ['球幕电影', '4D电影'],
				weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
				btntextList: ['去签到', '已过期(未检票)', '已取消', '去检票', '已完成', '已过期(未签到)', '场次取消'],
				pageNum: 1,
				pageSize: 5,
				total: 0,
				menuButtonInfo: {}
			}
		},
		components: {
			header,
		},
		created() {
			this.menuButtonInfo = uni.getMenuButtonBoundingClientRect();
		},
		onShow() {
			this.pageNum = 1;
			this.pageSize = 5;
			this.total = 0;
			this.filmSessionList = [];
			this.getFilmSessionList();
		},
		methods: {
			getFilmSessionList() {
				this.$myRequest({
					// url: "/web/fileSession/personalCenterFilm",
					url: "/apitp/Filmsession/personalCenterFilm",
					data: {
						pageNum: this.pageNum,
						pageSize: this.pageSize
					}
				}).then((res) => {
					let dataList = res.data.data.rows
					this.total = res.data.data.total;

					// dataList = res.data.data.rows.sort((a, b) => {
					// 	return new Date(b.filmStartTime.replace(/-/g, '/')) - new Date(a.filmStartTime
					// 		.replace(/-/g, '/'))
					// });

					let now = new Date().getTime()
					dataList.forEach((item) => {
						let tempStartTime = new Date(item.filmStartTime).getTime();
						if(tempStartTime > now){
							//判断场次是否取消
							if(item.filmPoll != null && item.inventoryVotes != null){
								let tempNum = item.filmType == 1 ? 8:5;
								if(now >= (tempStartTime - 10 * 60 * 1000)){
									if((item.filmPoll - item.inventoryVotes) < tempNum){
										item.subscribeState = 7;
									}								
								}
							}
						}
						this.filmSessionList.push({
							...item,
							// 当前时间是否到开始时间前三十分钟, 到则返回true, 没到就返回false
							// is30: Utils.timeThenNow(new Date(item.filmStartTime.replace(/-/g,
							// 	'/')) - 1000 * 60 * 30),
							filmStartTime: Utils.changeTime(item.filmStartTime.replace(/-/g, '/')),
							filmEndTime: Utils.changeTime(item.filmEndTime.replace(/-/g, '/')),
						});
					});
				})
			},
			pageChange() {
				if (this.filmSessionList.length >= this.total) {
					return;
				}
				this.pageNum++;
				this.getFilmSessionList();
			},
			cancelScheme(e) {
				uni.showModal({
					title: "是否取消预约",
					showCancel: true,
					success: (state) => {
						if (state.confirm) {
							let {
								vote,
								filmsessionid,
								batchnumber
							} = e.target.dataset
							let data = {
								vote: vote,
								filmSessionId: filmsessionid,
								batchNumber: batchnumber,
							}
							this.$myRequest({
								// url: '/web/fileSession/cancelFilmSession',
								url: '/apitp/Filmsession/cancelFilmSession',
								method: 'get',
								data
							}).then((res) => {
								this.filmSessionList.forEach(item => {
									if (item.filmSeeionId === filmsessionid) {
										item.subscribeState = '3';
									}
								})
								uni.showToast({
									title: '取消预约成功',
									duration: 2000
								})
								// this.filmSessionList = []
								// this.getFilmSessionList()
							})
						} else if (state.cancel) {}
					}
				})
			},
			toFilmQrcode(e) {
				let { batchnumber, filmseeionid } = e.currentTarget.dataset;

				// let filmSession = this.filmSessionList.find(function(item) {
				// 	return item.batchNumber == batchnumber
				// })

				// let filmInfo = {
				// 	filmName: filmSession.filmName,
				// 	filmType: filmSession.filmType,
				// 	filmStartTime: filmSession.filmStartTime,
				// 	filmEndTime: filmSession.filmEndTime,
				// 	filmArrangedDate: filmSession.filmArrangedDate,
				// 	filmCover: filmSession.filmCover,
				// 	subscribeState: filmSession.subscribeState,
				// 	isSurplus: filmSession.isSurplus
				// }
				uni.navigateTo({
					// url: `/pages/schemesuccess/filmsuccess?batchNumber=${batchnumber}&filmSessionId=${filmSession.filmSeeionId}`,
					url: `/pages_app/schemesuccess/filmsuccess?batchNumber=${batchnumber}&filmSessionId=${filmseeionid}`,
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.film_scheme {
		width: 100%;
		height: 100vh;
		font-family: "PingFang SC";
		background: #F3F4F6;

		.film_scheme_list {
			width: 100%;
			height: 100vh;

			.scheme {
				padding: 28upx 28upx 0upx 28upx;
				min-height: 90vh;
				box-sizing: border-box;

				.file_item {
					padding: 19upx 19upx 38upx 19upx;
					border-radius: 10upx;
					margin-bottom: 20upx;
					background-color: #fff;
					box-sizing: border-box;

					.scheme_item_title {
						font-size: 34upx;
						font-weight: 600;
						color: #000000;
					}

					.file_head {
						display: flex;
						margin: 28upx 0;
						padding: 20upx;
						background-color: #F3F4F6;
						border-radius: 10upx;

						.file_head_left {
							width: 144upx;
							height: 192upx;

							image {
								width: 100%;
								height: 100%;
								border-radius: 10upx;
							}
						}

						.file_head_right {
							margin-left: 28upx;
							display: flex;
							flex-direction: column;
							justify-content: space-around;
							flex-grow: 1;

							.file_name {
								font-size: 28upx;
								font-weight: 600;
								color: #000000;
							}

							&>view {
								font-size: 24upx;
								color: #888888;
							}
						}
					}

					.file_count {
						font-size: 28upx;
						font-weight: 600;
						color: #6F6F6F;

						text {
							color: #FFBA38;
						}
					}

					.file_btn {
						margin-top: 20upx;
						display: flex;
						justify-content: space-between;

						&>view {
							width: 308upx;
							height: 76upx;
							line-height: 76upx;
							text-align: center;
							font-size: 34upx;
							color: #FFFFFF;
							border-radius: 10upx;

							&.cancel_scheme {
								background-color: #ffba38;
								background-size: 100% 100%;
								box-shadow: 0px 6px 8px rgba(255, 186, 56, 0.34);
								margin-right: 42upx;
							}

							&.qrcode_check {
								background-color: #5cb7ff;
								background-size: 100% 100%;
								box-shadow: 0px 6px 8px rgba(92, 183, 255, 0.34);
								flex-grow: 1;
							}
						}
					}

					.file_cancel_btn {
						margin-top: 20upx;

						&>view {
							height: 76upx;
							line-height: 76upx;
							background: #888888;
							box-shadow: 0px 3px 6px rgba(218, 218, 218, 0.34);
							opacity: 0.3;
							text-align: center;
							font-size: 34upx;
							color: #FFFFFF;
							border-radius: 10upx;
						}
					}
				}
			}
		}
	}
</style>
