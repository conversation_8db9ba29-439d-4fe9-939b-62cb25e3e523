<?php

namespace app\common\model;

use think\Model;
use think\Db;
use app\common\model\VenueSubscribe;

class Venue extends Model
{

    

    

    // 表名
    protected $table = 'venue';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    
    public static function signVenue(array $params)
    {
        $linkIds = $params['linkIds'];
        $venueId = $params['venueId'];
        $userId  = $params['userId'];
        $total   = count($linkIds);
        // 场馆减票数， 添加用户预约记录
        return Db::transaction(function () use ($venueId, $userId, $linkIds, $total) {

            //  使用悲观锁防止超卖（新增）
            $venue = self::where('id', $venueId)
                ->lock(true)
                ->find();

            if (!$venue) {
                throw new \Exception('场馆不存在');
            }

            if ($venue['inventory_votes'] < $total) {
                throw new \Exception('场馆余票不足,请选择其他场次');
            }

            // 扣减余票
            $venue->inventory_votes -= $total;
            $venue->save();

            // 写入预约记录（此处同步，模拟 Java 中异步线程池）
            $data = [];
            foreach ($linkIds as $linkId) {
                $data[] = [
                    'venue_id'        => $venueId,
                    'user_id'         => $userId,
                    'user_linkman_id' => $linkId,
                    'subscribe_state' => '1',
                    'type'            => 0,
                    'number'          => $total
                ];
            }

            $result = (new VenueSubscribe)->insertAll($data);
            return $result;
        });
    }
    







}
