version: '3.9'

services:
  php:
    image: baoan/php-fpm-with-redis:v1.5   # ✅ 自定义镜像名
    build:
      context: ./docker/php
      dockerfile: Dockerfile
      args:
        CONTAINER_PATH: ${CONTAINER_PATH}  # 传给 Dockerfile 使用
    container_name: baoan-php-fpm  # ✅ 自定义容器名字
    ports:
      - "9000:9000"
    volumes:
      - ${CODE_PATH}:${CONTAINER_PATH}    # 宿主机 ↔ 容器挂载路径都用变量
    restart: unless-stopped
