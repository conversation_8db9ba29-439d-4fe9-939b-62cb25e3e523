<?php

namespace app\common\model;

use think\Model;

class WebUser extends Model
{
    // 显式绑定 Java 项目的 MySQL 表名
    protected $table = 'sys_user';

    protected $pk = 'user_id'; // 如果主键是 user_id

    // 模型类中
    protected $autoWriteTimestamp = 'datetime'; // 改为 'datetime'
    protected $dateFormat = 'Y-m-d H:i:s'; // 定义输出格式
    protected $createTime = 'create_time'; // 创建时间字段名
    protected $updateTime = 'update_time'; // 更新时间字段名

    /**
     * 获取会员的组别
     */
    public function getGroupAttr($value, $data)
    {
        return;
    }
}
