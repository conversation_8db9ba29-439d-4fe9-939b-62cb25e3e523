<template>
	<view class="filmDes">
		<view class="filmInfoContent">
			<Header menuClass="df" :isBack="true" :isShowHome="true" title="观影预约" :background="`transparent`"></Header>
			<view class="content">
				<view class="titlebar"></view>
				<view :class="['main',isChooseContact?'isChooseContact':'']">
					<view class="show_fixed">
						<view class="filmNew">
							<view class="filmItem">
								<view class="itemLeft">
									<image :src="film.filmCover" mode="aspectFill" class="cover"></image>
									<view class="fileInfo">
										<text class="filmName">{{film.filmName}}</text>
										<text class="filmType">类型：{{filmTypeList[film.filmType - 1]}}</text>
										<text class="filmTime">时间：{{film.filmStartTime}}-{{film.filmEndTime}}</text>
										<text class="filmDate">
											<text>日期：{{film.filmArrangedDate}}</text>
											<text
												style="margin-left: 5px;">{{weekList[new Date(film.filmArrangedDate).getDay()]}}</text>
										</text>
									</view>
								</view>
								<view class="itemRight">
									<view :class="['ticketType',isShowCountDown?'':'hidden']">
										<text class="n_text">倒计时</text>
										<text class="t_text">{{countDown}}</text>
									</view>
									<view
										:class="['order_button',isShowCountDown?'green':'',film.inventoryVotes==0?'gray':'']">
										剩余:{{film.inventoryVotes}}
									</view>
								</view>
							</view>
							<view class="filmText">
								{{film.fileIntroduce}}
							</view>
						</view>
						<view class="line">
							<view class="line_item" v-for="(item,index) in 20" :key="index"></view>
						</view>
						<view class="orderNum">
							<view class="orderTitle">
								预约人数
							</view>
							<view class="chooseNum">
								<view :class="['checkItem',checkIndex===index?'isCheck':'']"
									v-for="(item,index) in checkItem" :key="index" @click="checkNum(item.value)">
									{{item.label}}
								</view>
							</view>
						</view>
						<view class="line lineshow">
							<view class="line_item" v-for="(item,index) in 20" :key="index"></view>
						</view>
					</view>
					<view class="contact">
						<view class="contactTitle">
							人员名单
						</view>
						<view class="contactList">
							<view class="contactItem" v-for="(item,index) in linkList" :key="index">
								<view class="left">
									<view class="peopleName">
										{{item.linkmanName}}
									</view>
									<view class="peopleCard">
										身份证 {{ item.linkmanCertificate }}
									</view>
									<view class="peopleMablie">
										<text>手机号码 {{item.linkmanPhone}}</text>
										<text>年龄 {{item.linkmanAge}}</text>
									</view>
								</view>
								<view class="right">
									<view :class="['checkBtn','isCheck']"></view>
								</view>
							</view>
						</view>
					</view>
					<view class="show_foot">

					</view>
				</view>
			</view>
		</view>
		<view class="filmInfofont" :style="{'display':isChooseContact?'flex':'none'}">
			<view class="submitBtn" @click="submitSchemeFilm">
				提交
			</view>
		</view>
		<view class="mask" v-show="isShowMaskBlack">
			<view class="blackDefault">
				<view class="blackStateIcon"></view>
				<view class="blackStateT">
					<text>{{blackTips}}</text>
					<br />
					<text v-if="blackTipsDate">解封剩余<text class="blackDate">{{blackTipsDate}}</text></text>
				</view>
				<view class="blackBtn" @click="isShowMaskBlack=false">返回</view>
			</view>
		</view>
		<view class="mask" v-show="isShowMaskLocation">
			<view class="blackDefault">
				<view class="locationStateIcon"></view>
				<view class="blackStateT">
					<text>您的定位较远</text>
					<br />
					<text>请移步至宝安科技馆进行<text class="blackDate">现场签到</text></text>
				</view>
				<view class="blackBtn" @click="isShowMaskLocation=false">返回</view>
			</view>
		</view>
	</view>
</template>

<script>
	import Header from "@/component/header.vue";
	import Utils from '@/utils/index.js';
	export default {
		data() {
			return {
				isChooseContact: true,
				checkIndex: null,//这个就是选中“人数”的标识
				checkItem: [{
					label: "1人",
					value: "1"
				}, {
					label: "2人",
					value: "2"
				}, {
					label: "3人",
					value: "3"
				}, {
					label: "4人",
					value: "4"
				}, {
					label: "5人",
					value: "5"
				}],
				linkList: [],
				film: {},
				filmSessionId: null,
				filmTypeList: ['球幕电影', '4D电影'],
				weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
				isShowMaskBlack: false,
				blackTips: null,
				blackTipsDate: null,
				countDown: null,
				isShowCountDown: false,
				timer: null,
				isShowMaskLocation: false,
				batchnumber: null,
				subscribeSuccess:false,
				reservationsSuccess:false
			};
		},
		components: {
			Header,
		},
		onLoad(option) {
			this.filmSessionId = option.filmSessionId
		},
		onShow() {
			this.getFilm()
			if (uni.getStorageSync('gyyy_link')) {
				this.isChooseContact = true;
				this.linkList = JSON.parse(uni.getStorageSync('gyyy_link'));
				this.checkIndex = this.linkList.length - 1
			} else {
				this.isChooseContact = false;
			}
		},
		onHide() {
			uni.removeStorageSync('gyyy_link');
			clearInterval(this.timer)
		},
		onUnload() {
			clearInterval(this.timer)
		},
		methods: {
			//选择人数
			checkNum(index) {
				this.checkIndex = (index - 1);
				uni.navigateTo({
					// url: `/pages/contacts/index?num=${index}&type=gyyy_link`
					url: `/pages_app/contacts/index?num=${index}&type=gyyy_link`
				})
			},
			getFilm() {
				this.$myRequest({
					// url: '/web/fileSession/personalCenterFilmByFilmSessionId',
					url: '/apitp/Filmsession/personalCenterFilmByFilmSessionId',
					method: 'get',
					data: {
						filmSessionId: this.filmSessionId
					}
				}).then((res) => {
					this.film = {
						...res.data.data,
						filmStartTime: Utils.changeTime(res.data.data.filmStartTime.replace(/-/g, '/'), false),
						filmEndTime: Utils.changeTime(res.data.data.filmEndTime.replace(/-/g, '/'), false)
					}
					this.getCountDown()

				})
			},



			getCountDown() {
				clearInterval(this.timer)
				let signEndTime = new Date(this.film.filmArrangedDate.replace(/-/g, '/') + " " + this.film.filmStartTime).getTime()
				let filmArrangeDateTime = new Date(this.film.filmArrangedDate.replace(/-/g, '/')).getTime()
				let nowTime = new Date().getTime()
				let time = signEndTime - nowTime + filmArrangeDateTime
					
				this.isShowCountDown = (time > filmArrangeDateTime) && (time < 1000 * 60 * 30 +
					filmArrangeDateTime)
				this.timer = setInterval(() => {
					nowTime = new Date().getTime()
					if (!(signEndTime >= nowTime)) {
						clearInterval(this.timer)
						uni.showModal({
							title: '提示',
							content: '影片预约时间已过',
							showCancel: false,
							success: (res) => {
								if (res.confirm) {
									uni.navigateTo({
										url: '/pages_app/film/index'
									})
								}
							}
						})
					}
					this.isShowCountDown = (time > filmArrangeDateTime) && (time < 1000 * 60 * 30 +
						filmArrangeDateTime)
					time -= 1000
					this.countDown = this.date2MS(time)
				}, 1000)
			},
			date2MS(date) {
				let date1 = new Date(date)
				let m = date1.getMinutes() < 10 ? '0' + date1.getMinutes() : date1.getMinutes()
				let s = date1.getSeconds() < 10 ? '0' + date1.getSeconds() : date1.getSeconds()
				return m + ":" + s
			},

			submitSchemeFilm() {
				const baoanLocation = this.$baoanLocation
				// 请求判断是否是黑名单
				this.$myRequest({
					// url: '/web/fileSession/isBalckList',
					url: '/apitp/Filmsession/isBalckList',
					method: 'get'
				}).then((result) => {
					// 返回false则代表不是黑名单
					if (!result.data.data) {		
					
					
						let userLinkmanIdList = this.linkList.map((item) => {
							return item.id
						})
						let data = {
							filmSeeionId: this.filmSessionId,
							subscribeType: this.checkItem[this.checkIndex].value.toString(),
							isSurplus: this.isShowCountDown ? '1' : '0',
							userLinkmanIdList: userLinkmanIdList.toString()
						}
						this.$myRequest({
							// url: '/web/fileSession/reservations',
							url: '/apitp/Filmsession/reservations',
							method: 'post',
							data
						}).then((res) => {
							if (res.data.code !== 200) {
								uni.showToast({
									title: '预约失败'+res.data.msg,
									icon: 'none',
									duration: 4000
								})
								return;
							}
							this.batchnumber = res.data.data
							uni.showToast({
								title: '预约成功',
								icon: 'success'
							})					
							this.reservationsSuccess = true
							uni.setStorage('gyyy_link', null)
							this.subscribe()
						})

					} else {
						// 如果是黑名单, 返回的是解封时间, 如果是永久黑名单则返回1900-01-01
						let date = new Date(result.data.data)
						this.isShowMaskBlack = true
						if (date.getFullYear() == 1900) {
							this.blackTips = '您已永久进入黑名单'
						} else {
							this.isShowMaskBlack = true
							// 当前时间减去
							let nowDate = new Date().getTime()
							this.blackTips = '多次未核销进入黑名单'
							this.blackTipsDate = Math.ceil((date - nowDate) / (1000 * 60 * 60 * 24)) + '天'
						}
					}

				})
			},

			// 订阅消息通知
			subscribe() {
				// let tmplIds = ['-jlTze8tQ4-dZ-Yf1WYSKoY2DOKCqbRoGBxvZ3EkEOQ',
				// 	'RyGWxpWewL8UHZTHCh4gh-mVJsp4HddGjkj74-ynGps'
				// ]
				let tmplIds = ['sWn1mSByjsKEuiD-QOg48UWufz2idjYpr4FvjrrVAtY']
				let _this = this
				let flag = true // 是否勾选'总是保持以上选择'
				// this.$myRequest({
				// 	url: '/web/info/getInfo',
				// }).then((res) => {
				// 	console.log(1, res);
				// })

				// uni.getSetting({
				// 	withSubscriptions: true,
				// 	success(res) {
				// 		console.log(2, res);
				// 		tmplIds.forEach((item) => {
				// 			if (!res.subscriptionsSetting.itemSettings || !res.subscriptionsSetting
				// 				.itemSettings[item]) {
				// 				flag = false
				// 				// console.log(flag);
				// 			}
				// 		})
				// console.log(flag);
				uni.requestSubscribeMessage({
					tmplIds,
					success(res) {
						console.log('subscribe success', res);
						_this.subscribeSuccess = true
					},
					fail(err) {
						console.log('subscribe fail', err);
						_this.subscribeSuccess = true
					},
					complete() {
						console.log('subscribe complete');
						_this.subscribeSuccess = true
					}

				})
			},
		},
		watch:{
			subscribeSuccess(subscribeSuccess){
				if(subscribeSuccess && this.reservationsSuccess){
					uni.navigateTo({
						url: `/pages_app/schemesuccess/filmsuccess?batchNumber=${this.batchnumber}&filmSessionId=${this.filmSessionId}`
					});
				}
			},
			reservationsSuccess(reservationsSuccess){
				if(this.subscribeSuccess && reservationsSuccess){
					uni.navigateTo({
						url: `/pages_app/schemesuccess/filmsuccess?batchNumber=${this.batchnumber}&filmSessionId=${this.filmSessionId}`
					});
				}
			},
		}
	}
</script>

<style lang="less" scoped>
	.filmDes {
		width: 100%;
		height: 100vh;
		color: #fff;
		position: relative;
		font-family: 'PingFang SC';
		background: url('../../static/img/vieworder/initbg.png') no-repeat center top;
		background-color: blue;
		background-size: cover;
		overflow: scroll;

		.filmInfoContent {
			width: 100%;
			height: auto;
			min-height: 100vh;

			.content {
				width: 100%;
				height: auto;

				.titlebar {
					width: calc(~"100% - 30upx");
					height: 96upx;
					margin: 0 auto;
					background: url('../../static/img/vieworder/titlebar.png') no-repeat center top;
					background-size: cover;
					margin-top: 81upx;
				}

				.main {
					width: 654upx;
					height: 729upx;
					margin: -50upx auto 150upx auto;
					position: relative;

					.show_fixed {
						width: 100%;
						height: 100%;
						background: url(../../static/img/filmdes/filmdesbg.png) no-repeat center center;
						background-size: 100% 100%;


						.filmNew {
							width: 615upx;
							height: 494upx;
							margin: 0 auto;
							box-sizing: border-box;
							padding-top: 67upx;
							overflow: hidden;

							.filmItem {
								width: 100%;
								height: 231upx;
								margin: 0 auto 19upx auto;
								overflow: hidden;
								display: flex;
								justify-content: space-between;
								background: #F3F4F6;
								border-radius: 10upx;

								.itemLeft {
									width: auto;
									height: 100%;
									display: flex;
									align-items: center;
									margin-left: 19upx;

									.cover {
										width: 144upx;
										height: 192upx;
										margin-right: 12upx;
										border-radius: 10upx;
									}

									.fileInfo {
										height: 100%;
										padding-top: 27upx;
										box-sizing: border-box;

										.filmName {
											font-size: 29upx;
											font-weight: 600;
											color: #000000;
										}

										.filmType,
										.filmTime,
										.filmDate {
											width: 100%;
											display: block;
											font-size: 23upx;
											font-family: 'PingFang SC';
											color: #888888;
										}

										.filmType {
											margin: 19upx 0 13upx 0;
										}

										.filmTime {
											margin: 0 0 12upx 0;
										}
									}
								}

								.itemRight {
									width: 163upx;
									height: 100%;
									display: inline-block;
									position: relative;
									font-family: 'PingFang SC';
									position: relative;
									display: flex;
									align-items: center;

									.ticketType {
										height: 40upx;
										background: linear-gradient(180deg, #06E08B 0%, #02C975 100%);
										color: #F6F3F3;
										text-indent: 12upx;
										// margin-bottom: 67upx;
										border-radius: 0 20upx 0 0;
										position: absolute;
										top: 0;
										right: 0;
										padding-right: 35upx;

										&.hidden {
											visibility: hidden;
										}

										.n_text {
											font-size: 16upx;
											margin-right: 5upx;
										}

										.t_text {
											font-size: 24upx;
										}
									}

									.order_button {
										width: 135upx;
										height: 58upx;
										line-height: 58upx;
										text-align: center;
										background: linear-gradient(180deg, #FFCA5F 0%, #FFB33C 100%);
										box-shadow: 0upx 6upx 12upx rgba(255, 154, 54, 0.34);
										border-radius: 38upx;

										&.green {
											background: linear-gradient(180deg, #06E08B 0%, #02C975 100%);
											box-shadow: 0px 6px 12px rgba(2, 202, 118, 0.34);
										}

										&.gray {
											background: rgba(128, 128, 128, .6);
											box-shadow: 0upx 6upx 12upx rgba(128, 128, 128, 0.34);
										}

										&.remain_ticket {
											background: linear-gradient(180deg, #06E08B 0%, #02C975 100%);
											box-shadow: 0px 6px 12px rgba(2, 202, 118, 0.34);
										}
									}
								}
							}

							.filmText {
								color: #888888;
								font-size: 23upx;
								line-height: 38upx;
							}

						}

						.line {
							width: 90%;
							height: 1upx;
							position: relative;
							left: 50%;
							transform: translate(-50%, -50%);
							display: flex;
							justify-content: space-around;

							.line_item {
								width: 2%;
								height: 100%;
								background-color: #5f94dc;
							}

						}

						.orderNum {
							width: 615upx;
							height: auto;
							margin: 35upx auto 45upx auto;

							.orderTitle {
								color: #000000;
								font-size: 33upx;
								margin-bottom: 20upx;
								text-align: center;
								font-weight: 700;
							}

							.chooseNum {
								width: 100%;
								height: 58upx;
								// padding: 0 38upx;
								box-sizing: border-box;
								display: flex;
								justify-content: space-between;

								.checkItem {
									// width: 154upx;
									width: 100upx;
									height: 58upx;
									line-height: 58upx;
									text-align: center;
									border-radius: 10upx;
									color: #888888;
									font-size: 25upx;
									background: #F3F4F6;

									&.isCheck {
										background: #5CB7FF;
										color: #fff;
									}
								}
							}
						}

						.lineshow {
							display: none !important;
						}
					}

					.contact {
						width: 100%;
						height: auto;
						padding-top: 38upx;
						display: none;
						background-color: #fff;

						.contactTitle {
							color: #000000;
							font-size: 33upx;
							margin-bottom: 19upx;
							text-align: center;
							font-weight: 700;
						}

						.contactList {
							width: 100%;
							height: auto;

							.contactItem {
								width: 577upx;
								height: 158upx;
								box-sizing: border-box;
								padding: 0 29upx;
								display: flex;
								justify-content: space-between;
								margin: 0 auto;
								background: #F3F4F6;
								border-radius: 15upx;
								margin-bottom: 19upx;

								.left {
									width: auto;
									height: 100%;
									display: flex;
									flex-direction: column;
									justify-content: center;

									.peopleName {
										color: #000;
										font-size: 29upx;
										font-weight: 600;
										margin-bottom: 10upx;
									}

									.peopleCard,
									.peopleMablie {
										color: #888;
										font-size: 23upx;
									}

									.peopleMablie {
										text {
											&:first-child {
												display: inline-block;
												margin-right: 96upx;
											}
										}
									}
								}

								.right {
									width: 38upx;
									height: 100%;
									display: flex;
									align-items: center;

									.checkBtn {
										width: 38upx;
										height: 38upx;
										line-height: 38upx;
										text-align: center;
										border: 2upx solid #888888;
										border-radius: 38upx;
										font-weight: 700;

										&.isCheck {
											background: #FFBA38;
											color: #000;
											border: none;
											font-family: 'icongou';

											&::before {
												content: "\e66c";
												display: inline-block;
											}
										}
									}
								}

							}
						}
					}

					.show_foot {
						width: 654upx;
						height: 21upx;
						background: url(../../static/img/vieworder/content_foot.png) no-repeat center center;
						background-size: 100% 100%;
						display: none;
					}

					&.isChooseContact {
						width: 654upx;
						height: auto;

						.show_fixed {
							height: 737upx;
							background: url(../../static/img/vieworder/content_body.png) no-repeat center center;
							background-size: 100% 100%;


							.filmNew {
								height: 487upx;
							}

						}

						.line {
							display: none;
						}

						.contact {
							display: inline-block;
						}

						.show_foot {
							display: block;
						}

					}
				}

			}
		}

		.filmInfofont {
			width: 100%;
			height: 180upx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: fixed;
			bottom: 0;

			.submitBtn {
				width: 615upx;
				height: 77upx;
				line-height: 77upx;
				text-align: center;
				background: #5CB7FF;
				box-shadow: 0upx 6upx 12upx rgba(82, 162, 225, 0.34);
				border-radius: 10upx;
				font-size: 35upx;
			}
		}

		.mask {
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.5);
			position: fixed;
			left: 0;
			top: 0;

			.blackDefault {
				width: 615upx;
				height: 500upx;
				background: #FFFFFF;
				border-radius: 38upx;
				position: absolute;
				box-sizing: border-box;
				padding-top: 210upx;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);

				.blackStateIcon {
					width: 304upx;
					height: 304upx;
					position: absolute;
					top: 0;
					left: 0;
					transform: translate(50%, -50%);
					background: url(../../static/img/black/black_icon.png) no-repeat center center;
				}
				.locationStateIcon {
					width: 304upx;
					height: 304upx;
					position: absolute;
					top: 0;
					left: 0;
					transform: translate(50%, -50%);
					background: url(../../static/img/ordersate/sign_fail.png) no-repeat center center;
					background-size: 100% 100%;
				}
				.blackStateT {
					text-align: center;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%, -50%);
					font-size: 28upx;
					font-family: "PingFang SC";
					font-weight: 400;
					color: #888;

					.blackDate {
						color: #FE8315;
					}
				}

				.blackBtn {
					width: 381upx;
					height: 77upx;
					line-height: 77upx;
					text-align: center;
					border-radius: 10upx;
					margin: 0 auto;

					font-size: 34upx;
					font-family: PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					position: absolute;
					bottom: 78upx;
					left: 50%;
					transform: translateX(-50%);


					background: linear-gradient(180deg, #F2AF30 0%, #FF8315 100%);
					box-shadow: 0upx 6upx 12upx rgba(245, 189, 84, 0.34);
				}
			}
		}
	}
</style>
