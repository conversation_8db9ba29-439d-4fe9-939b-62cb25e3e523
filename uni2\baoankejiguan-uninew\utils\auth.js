// common/utils/auth.js
export function checkLoginPromise(context) {
	const token = uni.getStorageSync('token');
	if (token) {
		context.userInfo = uni.getStorageSync('userInfo');
		return Promise.resolve(context.userInfo);
	}

	return new Promise((resolve, reject) => {
		uni.showModal({
			title: '温馨提示',
			content: '授权微信登录后才能正常使用小程序功能',
			success: (res) => {
				if (res.cancel) {
					uni.showToast({
						title: '您拒绝了请求，不能正常使用小程序',
						icon: 'none',
						// icon: 'error',
						duration: 2000
					});
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/index/index'
						});
					}, 2000);
					return reject(new Error('用户取消'));
				}

				uni.login({
					provider: 'weixin',
					success: (loginRes) => {
						context.$myRequest({
							url: '/apitp/user/wxlogin',
							method: 'GET',
							data: { code: loginRes.code }
						}).then(res => {
							if (res.data.code === 200) {
								const { token, user } = res.data.data;
								uni.setStorageSync('token', token);
								uni.setStorageSync('userInfo', user);
								context.userInfo = user;
								uni.showToast({ title: '登录成功' });
								resolve(user);
							} else {
								reject(new Error(res.data.msg));
								uni.showToast({
									title: '登录异常：' + res.data.msg,
									icon: 'error',
									duration: 5000
								});
							}
						}).catch(err => {
							uni.showToast({
								title: '登录异常：' + err.errMsg,
								icon: 'error',
								duration: 5000
							});
							reject(err);
						});
					}
				});
			}
		});
	});
}
