<?php

namespace app\apitp\controller;

use app\common\controller\Api;

/**
 * 首页接口
 */
class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 首页
     *
     */
    public function index()
    {
        $this->success('请求成功');
    }    
    
    public function index1()
    {
     // 示例：加密一段 JSON
    $data = json_encode([
        "venueId" => 8058,
        "linkmanName" => "龙测试"
    ], JSON_UNESCAPED_UNICODE);

    $encrypted = VenueQrCodeCipher::encrypt($data);
    // echo "加密结果：$encrypted\n";

    // 解密回原始内容
    $encrypted="venue:1248040146555C5A5F5257584A43190C071F082C004D514F5E535C50444D4D1C1A0F1F2C004D515E545058585F4D4D050004062C004D514F53585F5150544D454B191807170C190404043C1C07150A4B53485C47484D1804010F3C1C07150A4B53485D47484D1F1416044D52564D4D071C070F00164D515C4A430301080A020807240C08014D514FDEBADFACA79BD9974B464F090D010000070F3F00090F0A4B53485C565C5F5B5C55595F585243434B0503030E090E052C01044D525555434B0503030E090E052E03131B0100080C081D0F4F5F4629595E52535B5A55554D454B06040B0F020A0325041D1C0F07060A081E08311D1F0E4F5C435E4A4A431C01061D390C090A495744535F5A534C5F51445A5945555B515E505B5D5C441C";
    $decrypted = VenueQrCodeCipher::decrypt($encrypted);
    $decrypted = json_decode($decrypted, true);
    echo "解密内容：";    
    print_r($decrypted);
    
    
    $encrypted="venue:124804014655535F51515B5E4A43190C071F082C004D51555750574444141C0C1B2309475E5C5A5A5E59584444141C0C1B26040B0F020A032F054D5253595C51595E4147171A091E0513060A03321B081D0F4F5F465E4941441516180343555945480310090D0E1F445B5E444412060E07391904100A495744514D4444081C2A05051E004655495D444D4D040F0F04200D4857505C5C535D524D4D040F0F040408042304090A495744A095DA84ABBB4B4548010C0A04060C0831070708044D534B5B5E5D545F5A5E5E515F58444D4D050004060805012A0A0343555C524D4D0500040608050128081415060E0F020E1D0C485747505B5B5A57505E515E515F50595F5B55565C4941440D06060D0C0E072A0F1F110D09020E07150A3C1F110A4B53485D47484D180509163B010B044D534B585D5751425B554B515B485755555A5E5058534612";
    $decrypted = VenueQrCodeCipher::decrypt($encrypted);
    $decrypted = json_decode($decrypted, true);
    echo "解密内容：";    
    print_r($decrypted);
    }



}


class VenueQrCodeCipher
{
    // COMM_KEY 等同于 Java 中的 byte[]{105,106,109,101,100,111,107,109,102,97,111,104,102,97,111,105}
    private static $COMM_KEY = [105,106,109,101,100,111,107,109,102,97,111,104,102,97,111,105];

    /**
     * 加密函数：传入原始字符串，返回 venue: 开头的 HEX 字符串
     */
    public static function encrypt($plaintext)
    {
        $bytes = mb_convert_encoding($plaintext, 'GBK', 'UTF-8'); // 编码兼容 GBK
        $byteArray = unpack('C*', $bytes);
        $encrypted = [];

        $keyLen = count(self::$COMM_KEY);
        foreach ($byteArray as $i => $b) {
            $j = ($i - 1) % $keyLen;
            $encrypted[] = $b ^ self::$COMM_KEY[$j];
        }

        $hex = strtoupper(implode('', array_map(function($v) {
            return str_pad(dechex($v), 2, '0', STR_PAD_LEFT);
        }, $encrypted)));

        return 'venue:' . $hex;
    }

    /**
     * 解密函数：传入 venue: 开头的密文，返回原始 JSON 字符串
     */
    public static function decrypt($cipherText)
    {
        if (strpos($cipherText, 'venue:') === 0) {
            $cipherText = substr($cipherText, 6);
        }

        $bytes = [];
        for ($i = 0; $i < strlen($cipherText); $i += 2) {
            $bytes[] = hexdec(substr($cipherText, $i, 2));
        }

        $keyLen = count(self::$COMM_KEY);
        $decrypted = '';
        foreach ($bytes as $i => $b) {
            $j = $i % $keyLen;
            $decrypted .= chr($b ^ self::$COMM_KEY[$j]);
        }

        return mb_convert_encoding($decrypted, 'UTF-8', 'GBK');
    }
}
