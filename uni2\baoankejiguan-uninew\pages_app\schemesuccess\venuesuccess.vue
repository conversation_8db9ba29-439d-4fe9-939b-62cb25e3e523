<template>
  <view class="venue_success">
    <view class="content">
      <Header
        :isFixed="true"
        menuClass="bor"
        :isBack="true"
        :isShowHome="true"
        title="预约成功"
        :background="`#fff`"
        :color="'#000'"
      >
      </Header>
      <Header :background="`transparent`"> </Header>
      <view class="scheme">
        <view class="scheme_state">
          <view class="scheme_state_head">
            <image
              src="../../static/img/schemesuccess/schemestate_course_venue.png"
              mode=""
            ></image>
            <view>预约成功</view>
          </view>
          <view class="info_content">
            <view class="scheme_state_tips">
              <view class="title"> 宝安科技馆 参观预约 </view>
              <view class="tip_item">
                <text class="ordinary">预约日期：</text>
                <text class="imp">{{ venueInfo.date }}</text>
              </view>
              <view class="tip_item">
                <text class="ordinary">入馆时间：</text>
                <text class="imp"
                  >{{ venueInfo.isMorning ? "上午场" : "下午场"
                  }}{{ venueInfo.times }}</text
                >
              </view>
              <view class="tip_item">
                <text class="ordinary">预约人数：</text>
                <text class="sp"
                  >{{
                    venueInfo.peoples.length
                  }}人【单击人员信息切换入场码】</text
                >
              </view>
            </view>
            <view class="contactList">
              <view
                class="contactItem"
                v-for="(item, index) in venueInfo.peoples"
                :key="index"
                @tap="choosePeople(index)"
              >
                <view class="left">
                  <view class="peopleName">
                    {{ item.linkmanName }}
                  </view>
                  <view class="peopleCard">
                    身份证 {{ item.linkmanCertificate }}
                  </view>
                  <view class="peopleMablie">
                    <text>手机号码 {{ item.linkmanPhone }}</text>
                    <text>年龄 {{ item.linkmanAge }}</text>
                  </view>
                  <view class="peopleCard">
                    预约号 {{ item.appointmentNo }}
                  </view>
                </view>
                <view class="right">
                  <view
                    :class="['checkBtn', item.checked ? 'isCheck' : '']"
                  ></view>
                </view>
              </view>
            </view>
            <view class="inner"> 凭此证进场 </view>
            <view class="scheme_tips">
              <view class="icons icon_warn"></view>
              在首页
              <text>个人中心—场馆预约—查看凭证</text>
              中查看此凭证
            </view>
          </view>
        </view>
        <view class="qr-wrapper" style="position: relative" v-if="qrCodeValue">
          <image class="qr-image" :src="qrCodeValue" />
        </view>
        <button
          type="default"
          :class="['inner_venue', signed ? 'signed' : '']"
          @click="signUp"
        >
          {{ signed ? "已签到" : "入馆签到" }}
        </button>
      </view>
      <view class="mask" v-show="isShowMask">
        <view :class="['signDefault', signState === 'fail' ? 'signFail' : '']">
          <template v-if="signState === 'success'">
            <view class="signStateIcon"></view>
            <view class="signStateT">恭喜您，签到成功！</view>
            <view class="signBtn" @click="isShowMask = false">确定</view>
          </template>
          <template v-else-if="signState === 'fail'">
            <view class="signStateIcon"></view>
            <view class="signStateT">
              您的定位较远
              <br />
              请移步至宝安科技馆进行<text>现场签到</text>
            </view>
            <view class="signBtn" @click="isShowMask = false">返回</view>
          </template>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Header from "@/component/header.vue";
// import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue';
import Utils from "@/utils/index.js";
import baseConfig from "@/utils/baseConfig.js";
import drawQrcode from "@/common/js/wxqrcode.js";

let a = 0; //亮度
export default {
  data() {
    return {
      // ✅ 二维码相关字段（完全保留原样）
      qrCodeValue: "",
      curIndex: 0, // 当前联系人下标
      timer: null,

      isShowMask: false,
      signState: "success",
      signed: false, // 是否已经签到完
      venueInfo: {
        peoples: [], // 避免渲染前为 undefined
      },
      selectPeople: null, // 当前选中的联系人

      isOrder: false,
      id: null,
      isShowQuickClick: false,
    };
  },
  components: {
    Header,
  },
  onLoad(option) {
    // #ifdef MP-WEIXIN
    uni.getScreenBrightness({
      success: (res) => {
        a = res.value;
        uni.setScreenBrightness({ value: 0.5 });
      },
    });
    // #endif
    let { id, type } = option;
    this.id = id;
    if (type) {
      this.isOrder = true;
    }
    this.getVenueInfo(id);
  },
  onUnload() {
    // #ifdef MP-WEIXIN
    if (a > 0) {
      uni.setScreenBrightness({
        value: a,
        success: () => {},
      });
    }
    // #endif
    clearTimeout(this.timer);
  },
  methods: {
    getVenueInfo(id) {
      this.$myRequest({
        url: "/apitp/venue/showVoucher",
        data: {
          venueId: id,
        },
      }).then((res) => {
        if (res.data.code === 200) {
          let data = res.data.data;
          data["date"] = data.venueStartTime.slice(0, 10).replace(/-/g, ".");
          data["isMorning"] = new Date(data.venueStartTime).getHours() < 12;
          data["times"] =
            Utils.changeTime(data.venueStartTime) +
            "-" +
            Utils.changeTime(data.venueEndTime);
          // data['appointmentNo'] = data.peoples[0].linkmanCertificate.substring((data.peoples[0].linkmanCertificate.length - 6),data.peoples[0].linkmanCertificate.length)
          data.peoples.forEach((item) => {
            item.appointmentNo = item.linkmanCertificate.substring(
              item.linkmanCertificate.length - 6,
              item.linkmanCertificate.length
            );
          });
          // ✅ 获取场馆信息后初始化二维码
          if (data.signState === "1") {
            this.signed = true;
          }
          this.venueInfo = data;
          if (data.peoples.length > 0) {
            // 数据初始化完后调用
            this.initPeopleCheckStatus();
            this.getQrcode(0);
          }
        }
      });
    },
    // ✅ 获取二维码（每人独立）
    getQrcode(index) {
      this.curIndex = index;
      if (this.timer) clearTimeout(this.timer);

      const linkId = this.venueInfo.peoples?.[index]?.linkId;
      if (!this.id || !linkId) return;

      this.$myRequest({
        url: "/apitp/venue/getQrCode",
        data: {
          venueId: this.id,
          linkId: linkId,
        },
      }).then((res) => {
        if (res.data.code === 200) {
          // ✅ 替换旧 drawImg：直接赋字符串内容
          var i = res.data.data ? res.data.data : " ";
          this.qrCodeValue = drawQrcode.createQrCodeImg(i, {
            typeNumber: 4,
            errorCorrectLevel: "M",
            size: 400,
          });
          // ✅ 设置定时器：60 秒后刷新
          this.timer = setTimeout(() => {
            this.getQrcode(this.curIndex);
          }, 60000);
        }
      });
    },
    //初始化选中
    initPeopleCheckStatus() {
      if (this.venueInfo.peoples.length > 0) {
        this.venueInfo.peoples.forEach((item, index) => {
     // this.$set(item, "checked", index === 0); // 默认第一项 checked=true
        this.$set(item, "checked",true); // 改为全部都是选中
        });
        this.selectPeople = this.venueInfo.peoples[0]; // 默认第一项，用于提交数据
      }
    },
    choosePeople: function (e) {
      this.getQrcode(e);
    },
    //这个是可以变更选中状态的，但是目前不需要
    choosePeople111111(index) {
      // ✅ 更换选中状态
      this.venueInfo.peoples.forEach((item, i) => {
        item.checked = i === index;
      });
      this.selectPeople = this.venueInfo.peoples[index]; // 记录当前选中项，用于提交数据
      //获取当前选中联系人的二维码
      this.getQrcode(index);
    },
    // jumpToUser() {
    // 	uni.navigateTo({
    // 		url: '/pages/user/venuescheme'
    // 	});
    // },
    signUp() {
      if (this.signed) return;
      if (this.isShowQuickClick) {
        uni.showToast({
          title: "操作过快!!!",
          icon: "error",
        });
        return;
      }

      uni.showLoading({
        title: "获取位置信息中",
        mask: true,
      });

      // 根据 NODE_ENV 动态替换为本地地址
      let baoanLocation = baseConfig.baoanLocation;
      if (process.env.NODE_ENV === "development") {
        baoanLocation = baseConfig.LongLocation;
        console.log("获取 科技馆位置： 【LongLocation】 = ", baoanLocation);
      } else {        
        console.log("获取 科技馆位置： baoanLocation = ", baoanLocation);
      }

      const doSign = () => {
        this.$myRequest({
          // url: '/auth/venue/centerSign',
          url: "/apitp/venue/centerSign",
          data: {
            venueId: this.id,
            linkId: this.selectPeople["linkId"],
          },
        }).then((res) => {
          if (res.data.code === 200) {
            this.isShowMask = true;
            this.signed = true;
            this.signState = "success";
          } else {
            uni.showModal({
              title: "提示",
              content: res.data.msg,
              showCancel: false,
            });
          }
        });
      };
      const checkLocation = (res) => {
          console.log("手机位置res = ", res);
          if (
            Utils.distance(
              res.latitude,
              res.longitude,
              baoanLocation.la,
              baoanLocation.lo
            ) > 0.25
          ) {
            this.isShowMask = true;
            this.signState = "fail";
          } else {
            console.log("selectPeople = ", this.selectPeople);
            doSign();
          }
      };
      uni.getLocation({
        type: "gcj02",
        // #ifdef APP-PLUS
        isHighAccuracy: true,// 仅 App 有效，小程序忽略，H5 可能报错
        // #endif
        success: (res) => {
          checkLocation(res);
        },

        fail: (res) => {
          console.log("fail",res)
          this.isShowQuickClick = true;

          uni.getSetting({
            success: (res) => {
              const auth = res.authSetting["scope.userLocation"];

              console.log("1 =", typeof auth);
              console.log("2 =", auth);

              if (typeof auth !== "undefined" && !auth) {
               // ❌ 用户明确拒绝了授权          
                uni.showModal({
                  title: "提示",
                  content: "您拒绝了定位权限，将无法使用签到功能",
                  success: (res) => {
                    if (res.confirm) {
                      // #ifdef MP-WEIXIN
                      uni.openSetting({
                        success: (res) => {
                          if (res.authSetting["scope.userLocation"]) {
                            uni.getLocation({
                              type: "gcj02",
                              success: (res) => {
                                checkLocation(res);
                              },
                              fail: () => {
                                uni.showToast({
                                  title: "定位失败，请重试",
                                  icon: "none",
                                });
                              },
                            });
                          } else {
                            uni.showToast({
                              title: "您拒绝了定位权限，将无法使用签到功能",
                              icon: "none",
                            });
                          }
                        },
                      });
                      // #endif

                      // #ifndef MP-WEIXIN
                      uni.getLocation({
                        type: "gcj02",
                        success: (res) => {
                          checkLocation(res);
                        },
                        fail: () => {
                          uni.showToast({
                            title: "定位失败，请检查权限设置",
                            icon: "none",
                          });
                        },
                      });
                      // #endif
                    }
                  },
                });
              }else if (auth === true) {
                // ✅ 用户已授权，就应该重新尝试定位
                uni.getLocation({
                  type: "gcj02",
                  success: (res) => {
                    checkLocation(res); // 调用你封装的定位判断逻辑
                  },
                  fail: () => {
                    uni.showToast({
                      title: "定位失败，请稍后重试",
                      icon: "none"
                    });
                  }
                });
              }
            },
            fail: (error) => {
              console.error('getSetting 调用失败：', error);
              uni.showToast({
                title: '权限读取失败',
                icon: 'none'
              });
            }
          });
        },
        complete: () => {
          setTimeout(() => {
            this.isShowQuickClick = false;
          }, 10000);
          uni.hideLoading();
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.venue_success {
  width: 100%;
  height: 100vh;
  font-family: "PingFang SC";
  overflow: scroll;
  background: #f3f4f6;

  .content {
    width: 100%;
    height: auto;
    position: relative;

    .scheme {
      padding: 28upx;

      .scheme_state {
        border-radius: 10upx;

        & > view {
          box-sizing: border-box;
        }

        .scheme_state_head {
          width: 692upx;
          height: 298upx;
          margin: 0 auto;
          text-align: center;
          padding: 48upx 0 79upx 0;
          margin-bottom: 29upx;
          background: #ffffff;
          border-radius: 10upx;

          & > image {
            width: 208upx;
            height: 142upx;
            margin-bottom: 38upx;
          }

          & > view {
            font-size: 34upx;
            font-weight: 600;
            color: #5cb7ff;
            letter-spacing: 6upx;
          }
        }

        .info_content {
          width: 100%;
          height: auto;
          background-color: #fff;
          padding: 19upx 17upx 25upx 21upx;
          border-radius: 10upx;

          .scheme_state_tips {
            .title {
              font-size: 35upx;
              font-weight: 600;
              color: #000000;
              margin-bottom: 29upx;
            }

            .tip_item {
              margin-bottom: 19upx;
            }

            .imp {
              font-size: 29upx;
              color: #000000;
            }

            .ordinary {
              font-size: 27upx;
              color: #6f6f6f;
            }

            .sp {
              font-size: 29upx;
              color: #ffba38;
            }
          }

          .contactList {
            width: 100%;
            height: auto;
            overflow: auto;

            .contactItem {
              height: 158upx;
              box-sizing: border-box;
              padding: 0 26upx;
              display: flex;
              justify-content: space-between;
              background-color: #f3f4f6;
              margin-bottom: 20upx;
              border-radius: 10upx;

              .left {
                width: auto;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;

                .peopleName {
                  color: #000;
                  font-size: 29upx;
                  font-weight: 600;
                  margin-bottom: 10upx;
                }

                .peopleCard,
                .peopleMablie {
                  color: #888;
                  font-size: 23upx;
                }

                .peopleMablie {
                  text {
                    &:first-child {
                      display: inline-block;
                      margin-right: 96upx;
                    }
                  }
                }
              }

              .right {
                width: 38upx;
                height: 100%;
                display: flex;
                align-items: center;

                .checkBtn {
                  width: 38upx;
                  height: 38upx;
                  line-height: 38upx;
                  text-align: center;
                  border: 2upx solid #888888;
                  border-radius: 38upx;
                  font-weight: 700;

                  &.isCheck {
                    background: #ffba38;
                    color: #000;
                    border: none;
                    font-family: "icongou";

                    &::before {
                      content: "\e66c";
                      display: inline-block;
                    }
                  }
                }
              }
            }
          }

          .inner {
            font-size: 27upx;
            color: #000000;
            text-align: center;
            font-weight: 600;
            border-bottom: 1px solid #888888;
            padding-bottom: 38upx;
            margin-top: 9upx;
          }

          .scheme_tips {
            margin-top: 28upx;
            font-size: 24upx;
            font-family: "PingFang SC";
            font-weight: 400;
            color: #888888;
            display: flex;

            .icons {
              width: 38upx;
              height: 38upx;
              font-size: 38upx;
              margin-right: 19upx;
              color: #ffba38;
            }

            text {
              color: #ffba38;
            }
          }
        }
      }

      .inner_venue {
        width: 346upx;
        height: 77upx;
        text-align: center;
        line-height: 77upx;
        font-size: 35upx;
        color: #fff;
        background-color: #5cb7ff;
        margin-top: 18upx;
        margin-bottom: 25upx;
        border-radius: 10upx;
        box-shadow: 0upx 6upx 12upx rgba(92, 183, 255, 0.5);

        &::after {
          border: none;
        }

        &.signed {
          background-color: #888888;
          box-shadow: 0px 6upx 12upx rgba(136, 136, 136, 0.34);
        }
      }
    }

    .filmInfofont {
      width: 100%;
      height: 180upx;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;

      .submitBtn {
        width: 615upx;
        height: 77upx;
        line-height: 77upx;
        text-align: center;
        background: #5cb7ff;
        box-shadow: 0upx 6upx 12upx rgba(82, 162, 225, 0.34);
        border-radius: 10upx;
        font-size: 35upx;
        color: #fff;
      }
    }
  }

  .mask {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    left: 0;
    top: 0;

    .signDefault {
      width: 615upx;
      height: 500upx;
      background: #ffffff;
      border-radius: 38upx;
      position: relative;
      box-sizing: border-box;
      padding-top: 210upx;
      position: relative;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      .signStateIcon {
        width: 304upx;
        height: 304upx;
        position: absolute;
        top: 0;
        left: 0;
        transform: translate(50%, -50%);
        background: url(../../static/img/ordersate/sign_success.png) no-repeat
          center center;
        background-size: 100% 100%;
      }

      .signStateT {
        font-size: 42upx;
        color: #5cb7ff;
        text-align: center;
        font-weight: 600;
        margin-bottom: 77upx;
      }

      .signBtn {
        width: 381upx;
        height: 77upx;
        line-height: 77upx;
        text-align: center;
        background: #5cb7ff;
        box-shadow: 0upx 6upx 12upx rgba(82, 162, 225, 0.34);
        border-radius: 10upx;
        margin: 0 auto;
        font-size: 35upx;
        color: #fff;
      }

      &.signFail {
        .signStateIcon {
          background: url(../../static/img/ordersate/sign_fail.png) no-repeat
            center center;
        }

        .signStateT {
          color: #888888;
          font-size: 29upx;

          text {
            color: #ffba38;
          }
        }

        .signBtn {
          background: linear-gradient(180deg, #ffca5f 0%, #ffb33c 100%);
          box-shadow: 0px 6px 12px rgba(245, 189, 84, 0.34);
        }
      }
    }
  }

  .qr-wrapper {
    position: relative; /* ✅ 加回原始 position */
  }

  .qr-image {
    width: 480rpx;
    height: 480rpx;
    margin: 20rpx 90rpx 40rpx; /* ✅ 使用 margin 保持一致 */
  }
}
</style>
