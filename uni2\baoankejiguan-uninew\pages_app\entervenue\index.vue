<template>
	<view class="entervenue">
		<view class="content">
			<Header menuClass="df" :isBack="true" :isShowHome="true" title="参观预约" :background="`transparent`">
			</Header>
			<view class="venue_content">
				<view class="venue_top">
					<view class="calendar">
						<uni-calendar :date="selectedDate" :insert="true" :lunar="false" :showMonth="false"
							:start-date="timeSection.start" :end-date="timeSection.end" :selected="selected"
							@change="getVenueDetail" />
					</view>
				</view>
				<view :class="['venue_bottom',isChooseVenue?'isChooseVenue':'']">

					<view class="show_fixed_box">

						<!-- 蓝色顶部标签 -->
						<view class="reservation-header">
							个人预约
						</view>

						<UniSection title="*预约场次" type="line" />


						<view class="venue_list">
							<!-- <view :class="['venue_item',item.id===venueId?'venue_active':'']"
								v-for="(item,index) in venueInfo" :key="item.id" @click="chooseVenue(item)">
								<view>
									{{new Date(checkDate.replace(/-/g,'/')+' '+item.venueStartTime).getHours() < 12? "上午场": "下午场"}}
								</view>
								<view>
									{{item.venueStartTime}}-{{item.venueEndTime}}
								</view>
								<view>
									剩余：{{item.inventoryVotes}}
								</view>
							</view> -->
							<view class="venue_picker">
								<uni-data-picker :localdata="localdata" placeholder="点击选择入馆时间" popup-title="请选择时间"
									v-model="venueId" @change="bindPickerChange"></uni-data-picker>
							</view>
						</view>
						<!-- <view class="venue_tip" v-else>
							暂无场次数据
						</view> -->
						<!-- <view class="line">
							<view class="line_item" v-for="(item,index) in 20" :key="index"></view>
						</view> -->
						<view class="title">
							预约人数
						</view>
						<view class="chooseNum">
							<view :class="['checkItem',checkIndex===index?'isCheck':'']"
								v-for="(item,index) in checkItem" :key="index" @click="checkNum(item.value)">
								{{item.label}}
							</view>
						</view>
						<view class="line">
							<view class="line_item" v-for="(item,index) in 20" :key="index"></view>
						</view>
					</view>
					<view class="isChoose">
						<view class="title">
							人员名单
						</view>
						<view class="contactList">
							<view class="contactItem" v-for="(item,index) in linkList" :key="index">
								<view class="left">
									<view class="peopleName">
										{{item.linkmanName}}
									</view>
									<view class="peopleCard">
										身份证 {{ item.linkmanCertificate }}
									</view>
									<view class="peopleMablie">
										<text>手机号码 {{item.linkmanPhone}}</text>
										<text>年龄 {{item.linkmanAge}}</text>
									</view>
								</view>
								<view class="right">
									<view :class="['checkBtn','isCheck']"></view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="venceInfofont" v-if="isChooseVenue">
				<view class="submitBtn" @click="submitVenue">
					提交
				</view>
			</view>
			<view class="mask" v-show="isShowMask">
				<view class="maskContent">
					<view class="noticeTitle">预约须知</view>
					<view class="noticeView">
						<view class="noticeItem" v-for="(item,index) in noticeList" :key="index">
							<view class="itemTitle">
								{{item.title}}
							</view>
							<view class="itemContent" v-for="(childItem,childIndex) in item.view" :key="childIndex">
								{{childItem}}
							</view>
						</view>
					</view>
					<view :class="['agreeBtn',isShowGray?'gray':'']" @click="agree">
						{{readText}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>

	import Header from '@/component/header.vue';
	import UniCalendar from '@/uni_modules/uni-calendar/components/uni-calendar/uni-calendar.vue';
	import UniSection from '@/uni_modules/uni-section/components/uni-section/uni-section.vue'
	import UniDataPicker from '@/uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker.vue';
	// import UniEasyinput from '@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue';
	// import {		Header 	} from '@/component/header.vue';
	// import {		uniCalendar	} from '@/uni_modules/uni-calendar/components/uni-calendar/uni-calendar.vue'
	// import {		uniDataPicker	} from '@/uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker.vue'
	// import UniSegmentedControl from '@/uni_modules/uni-segmented-control/components/uni-segmented-control/uni-segmented-control.vue';

	import Utils from '@/utils/index.js';

	export default {
	data() {	
		return {
			// 核心状态
			isShowMask: false,
			isShowGray: false,
					copyVenueInfo: [],
			readTime: 5,		
			readText: '确定已阅读（5s）',
			times: null,
			fulldate: null,
			value: [],
			selectedDate: '',
			checkDate: '',
			timeSection: {
				start: '',
				end: ''
			},
			venueId: null,
			noticeList: [
				{ title: '一、预约对象', view: [] },
				{ title: '二、预约登记', view: [] },
				{ title: '三、入场审核', view: [] }
			],
			isChooseVenue: false,
			checkIndex: null,//这个就是选中“人数”的标识
			checkItem: [
				{ label: '1人', value: '1' },
				{ label: '2人', value: '2' },
				{ label: '3人', value: '3' },
				{ label: '4人', value: '4' },
				{ label: '5人', value: '5' }
			],
			linkList: [],
			selected: [],
			localdata: [
				{ text: '上午场', value: '1', 
					children: [
						/* {
												text: '10:00 - 11:00',
												value: '101',
												text1: '(剩余100)'
											}, {
												text: '11:00 - 12:00',
												value: '102',
												text1: '(剩余100)'
											} */
					]
				}, { text: '下午场', value: '2', 
					children: [
						/* {
												text: '12:00 - 13:00',
												value: '201',
												text1: '(剩余100)'
											}, {
												text: '13:00 - 14:00',
												value: '202',
												text1: '(剩余100)'
											} */
					]				}
					],
			venueInfo: []
		};
	},
	components: {
		Header,
		UniCalendar,
		// UniSegmentedControl,
		UniDataPicker,
		// UniEasyinput,
		UniSection
	},
	created() {
		this.showMask();
		this.getTimeSection();

		// 如果已登录，拉取预约数据
		const token = uni.getStorageSync('token');
		console.log('token=', token);
		if (token) {
			this.getVenueInfo();
		}
	},

	onShow() {
		this.downUrl = this.$config?.baseUrl + '/admin/venue/template/0';

		const token = uni.getStorageSync('token');
		const storageLink = uni.getStorageSync('rgyy_link');

		if (storageLink) {
			this.isChooseVenue = true;
			this.linkList = JSON.parse(storageLink);
			this.checkIndex = this.linkList.length - 1;
		} else {
			this.isChooseVenue = false;
		}

		// 如果没登录，引导微信登录
		// #ifdef MP-WEIXIN
		if (!token) {
			uni.showModal({
				title: '温馨提示',
				content: '授权微信登录后才能正常使用小程序功能',
				success: (res) => {
					if (!res.cancel) {
						uni.login({
							provider: 'weixin',
							success: (loginRes) => {
								this.$myRequest({
									// url: `/wx/user/${this.$config.appId}/login`,
									url: `/apitp/user/wxlogin`,
									method: 'GET',
									data: { code: loginRes.code }
								}).then(res => {
									if (res.data.code === 200) {
										const { token, user } = res.data.data;
										uni.setStorageSync('token', token);
										uni.setStorageSync('userInfo', user);
										this.userInfo = user;
										this.getVenueInfo();
										uni.showToast({ title: '登录成功' });
									}	
								}).catch(err => {
									uni.showToast({
										title: '登录异常：' + err.errMsg,
										icon: 'error',
										duration: 5000
									});
								});
							}
						});
					} else {
						uni.showToast({
							title: '您拒绝了请求，不能正常使用小程序',
							icon: 'error',
							duration: 2000
						});
					}
				}
			});
		}
		// #endif
	},

	onLoad(options) {
		// options.date = 2025-01-01
		this.selectedDate = options.date || '';
		this.checkDate = this.selectedDate;
	},

	methods: {
		//场馆须知，
		getlist() {
			this.$myRequest({
				// url: '/admin/entrance_announcement/1',
				url: '/apitp/announcement/getEntranceInfo',
				data: {
					id: 1,
				},
				method: 'get'
			}).then(res => {
				if (res.data.code === 200) {
					const data = res.data.data;
					this.noticeList[0].view = data.entranceObject.split('\n');
					this.noticeList[1].view = data.entranceRegister.split('\n');
					this.noticeList[2].view = data.entranceAudit.split('\n');
				}
			});
		},
		//计算当前时间
		getTimeSection() {
			let time = new Date();
			var oneDayTime = 24 * 60 * 60 * 1000;
			let start = time.getFullYear() + '-' + (time.getMonth() + 1) + '-' + time.getDate();
			let weekAfter = new Date(time.getTime() + oneDayTime * 6);
			let end = weekAfter.getFullYear() + '-' + (weekAfter.getMonth() + 1) + '-' + weekAfter.getDate();
			this.timeSection.start = start;
			this.timeSection.end = end;
		},
		showMask() {			
		
			this.getlist();

			const accountInfo = uni.getAccountInfoSync();
			this.isShowMask = true; //设置为 true 才会显示遮罩层
			this.isShowMask = accountInfo.miniProgram.envVersion === 'develop' ? false : true;
			this.isShowGray = true;
			this.readTime = 5;
			this.times = setInterval(() => {
				if (this.readTime === 1) {
					this.readText = '确定已阅读'
					this.isShowGray = false
					clearInterval(this.times);
				} else {
					this.readTime--;
					this.readText = `确定已阅读（${this.readTime}s）`;
				}
			}, 1000);
		},
		//点击 预约人数
		checkNum(index) {
			this.checkIndex = Number(index) - 1;
			uni.navigateTo({
				url: `/pages_app/contacts/index?num=${index}&type=rgyy_link`
			})
		},
		//点击场次项时判断是否为未来时间，设置当前选择的场次 ID。
		chooseVenue(venue) {
			if (!Utils.timeThenNow(this.checkDate.replace(/-/g, '/') + ' ' + venue.venueEndTime)) {
				uni.showModal({
					title: '提示',
					content: '该场次已结束',
					showCancel: false
				})
				return;
			}
			// 选择场馆的信息
			this.venueId = venue.id;
		},
		agree() {
			if (this.readTime === 1) {
				setTimeout(() => {
					this.isShowMask = false;
				}, 1000);
			}
		},
		//提交预约请求（仅“个人预约”模式）
		submitVenue() {
			/* if (this.venueInfo.length == 0) {
				uni.showModal({
					title: '提示',
					content: '当前日期没有场次',
					showCancel: false
				})
				return
			} */

			// 判断用户有没有选择场次
			console.log('this.venueId=', this.venueId);
			if (!this.venueId || this.venueId < 3) {
				// 进入这里：venueId 未定义、null、0、''，或小于 2
				uni.showModal({
					title: '提示',
					content: '请选择入馆时间',
					showCancel: false
				})
				return
			}
			let venue = this.copyVenueInfo.find((item) => {
				return item.id == this.venueId
			})
			if (!Utils.timeThenNow(venue.venueEndTime)) {
				uni.showModal({
					title: '提示',
					content: '该场次已结束',
					icon: 'error',
					showCancel: false
				})
				return
			}

			const linkIdList = this.linkList.map(item => item.id);
			if (linkIdList.length === 0) {
				return uni.showModal({
					title: '提示',
					content: '请选择联系人',
					showCancel: false
				});
			}
			// todo 
			this.$myRequest({
				// url: '/auth/venue/signVenue',
				url: '/apitp/venue/signVenue',
				method: 'post',
				data: JSON.stringify({
					linkIds: linkIdList,
					venueId: this.venueId,
				})
			}).then(res => {
				if (res.data.code === 200) {
					// uni.navigateTo({
					// 	url: `/pages/schemesuccess/venuesuccess?id=${this.venueId}&type=order`
					// });
					this.subscribe(); // 模板消息订阅 + 跳转
				}else{
					console.log('提交失败：', res.data.msg);
					return uni.showToast({
						title: res.data.msg,
						icon: 'none',
						duration: 2000,
						mask: true // ✅ 防止用户点击其他区域
					});
				}
			})
			
		},
		//  模板消息订阅 + 跳转
		subscribe() {
			const tmplIds = [
				'sWn1mSByjsKEuiD-QOg48UWufz2idjYpr4FvjrrVAtY',
				'sWn1mSByjsKEuiD-QOg48VlKvbjhcp_XfZpUmMJjt5g'
			];
			// 1. 获取用户订阅设置
			uni.getSetting({
				withSubscriptions: true,
				success: () => {
					// 2. 请求订阅消息
					uni.requestSubscribeMessage({
						tmplIds,
						success: (res) => {
							console.log('订阅成功', res);
							// 可选：记录成功的订阅结果
						},
						fail: (err) => {
							console.warn('订阅失败', err);
							// 可选：上报失败、打点日志
						},
						complete: () => {
							// 无论成功或失败，最终都跳转页面
							uni.navigateTo({
								url: `/pages_app/schemesuccess/venuesuccess?id=${this.venueId}&type=order`
							});
						}
					});
				},
				fail: (err) => {
					console.warn('获取订阅设置失败', err);
					// 即使获取失败也直接跳转
					uni.navigateTo({
						url: `/pages_app/schemesuccess/venuesuccess?id=${this.venueId}&type=order`
					});
				}
			});
		},
		//获取闭馆标记 + 场次初始时间段
		async getVenueInfo() {
			let res = await this.$myRequest({
				// url: '/auth/venue/getVenueInfo',
					url: '/apitp/venue/getVenueInfo',	
			});
			if (res.data.code === 200) {
				let dataList = res.data.data;
				this.selected = []; // 初始化标记数组
				// 标记闭馆日
				dataList.forEach(item => {
					var day = Utils.changeTime(item.day, true);
					if (item.isClose === '0') {
						this.selected.push({
							date: day,
							info: '闭馆'
						});
					}
				});
				// 获取第一个开放日（isClose = 1）  作为起始预约时间
				for (var i = 0; i < dataList.length; i++) {
					if (dataList[i].isClose === '1') {
						this.timeSection.start = Utils.changeTime(dataList[i].day, true);
						this.selectedDate = this.selectedDate || this.timeSection.start;
						break;
					}
				};
		
				if (this.timeSection.start) {
					this.getVenueDetail({
						fulldate: this.checkDate || this.timeSection.start
					});
				}
			}
		},
		//根据日期拉取场次详情（上午 / 下午）
		getVenueDetail(e) {
			this.checkDate = e.fulldate
			this.$myRequest({
				// url: '/auth/venue/getVenueDetail',
				url: '/apitp/venue/getVenueDetail',	
				method: 'post',
				data: {
					date: e.fulldate
				}
			}).then(res => {
				if (res.data.code === 200) {
					let dataList = res.data.data;
					this.copyVenueInfo = res.data.data
					// console.log(dataList);
					let morningData = []
					let afternoonData = []
					dataList.forEach((item) => {
						const timeStr = item.venueStartTime.replace(/-/g, '/');
						if (new Date(timeStr).getHours() < 12) {
							morningData.push({
								text: Utils.changeTime(item.venueStartTime) + ' - ' + Utils
									.changeTime(item.venueEndTime) +
									`    ( 剩余${item.inventoryVotes} )`,
								value: item.id,
								// text1: `(剩余${item.inventoryVotes})`
							})
						} else {
							afternoonData.push({
								text: Utils.changeTime(item.venueStartTime) + ' - ' + Utils
									.changeTime(item.venueEndTime) +
									`    ( 剩余${item.inventoryVotes} )`,
								value: item.id,
								// text1: `(剩余${item.inventoryVotes})`
							})
						}
					})

					this.localdata[0].children = morningData
					this.localdata[1].children = afternoonData
					// dataList.forEach(item => {
					// 	item.venueStartTime = Utils.changeTime(item.venueStartTime);
					// 	item.venueEndTime = Utils.changeTime(item.venueEndTime);
					// });
					// this.venueInfo = dataList;
					// if (dataList.length != 0) {
					// 	this.venueId = this.venueInfo[0].id;
					// }
				}
			})
		},
		bindPickerChange: function(e) {},
		onClickItem: function(e) {
			this.current !== e.currentIndex && (this.current = e.currentIndex)
		},		
	},
	onHide() {
	uni.removeStorageSync('rgyy_link');
	},
	onUnload() {
	uni.removeStorageSync('rgyy_link');
	},
}
</script>

<style lang="less" scoped>

	.entervenue {
		width: 100%;
		height: 100vh;
		background-color: #f8f8f8;
		background: url('../../static/img/vieworder/initbg.png') no-repeat center top;
		background-size: cover;
		color: #fff;
		overflow: scroll;
		font-family: 'PingFang SC';
		position: relative;

		.content {
			width: 100%;
			height: auto;
			position: absolute;

			.venue_content {
				width: 100%;
				height: auto;
				padding: 65upx 48upx 150upx 48upx;
				box-sizing: border-box;

				.venue_top {
					width: 100%;
					height: 638upx;
					margin-bottom: 20upx;
					background: url(../../static/img/entervenue/venue_top.png) no-repeat center center;
					background-size: 100% 100%;
					position: relative;
					

					.calendar {
						width: 100%;
						height: 92%;
						position: absolute;
						left: 0;
						bottom: 0;
					}

					&::before {
						content: '';
						width: 17upx;
						height: 62upx;
						background: url(../../static/img/entervenue/link_middle.png) no-repeat center center;
						background-size: 100% 100%;
						position: absolute;
						bottom: -38upx;
						left: 112upx;
					}

					&::after {
						content: '';
						width: 17upx;
						height: 62upx;
						background: url(../../static/img/entervenue/link_middle.png) no-repeat center center;
						background-size: 100% 100%;
						position: absolute;
						bottom: -38upx;
						right: 112upx;
					}
				}

				.venue_bottom {
					width: 100%;
					height: 562upx;
					margin-bottom: 330upx;

					.line {
						width: 95%;
						height: 1upx;
						position: relative;
						left: 50%;
						transform: translate(-50%, -50%);
						display: flex;
						justify-content: space-around;

						.line_item {
							width: 2%;
							height: 100%;
							background-color: #5f94dc;
						}
					}

					.title {
						text-align: center;
						font-size: 33upx;
						font-weight: 600;
						color: #000000;
						padding: 40upx 0 0 0;
					}

					.show_fixed_box {
						width: 100%;
						height: 100%;
						overflow: hidden;
						background: url(../../static/img/entervenue/venue_bottom.png) no-repeat center center;
						background-size: 100% 100%;

						.venue_list {
							width: 100%;
							height: 100upx;
							padding: 0upx 38upx;
							box-sizing: border-box;
							overflow: auto;
							// display: flex;
							// align-items: center;

							.venue_picker {
								width: 100%;
							}

							.venue_item {
								width: 100%;
								height: 58upx;
								margin-bottom: 19upx;
								background: #F3F4F6;
								padding: 0 27upx 0 17upx;
								box-sizing: border-box;
								display: flex;
								align-items: center;
								justify-content: space-between;
								color: #888888;
								font-size: 25upx;
								border-radius: 10upx;

								&.venue_active {
									background: #FFB833;
									color: #fff;
								}
							}
						}

						.venue_tip {
							width: 100%;
							height: 210upx;
							padding: 0upx 38upx;
							box-sizing: border-box;
							overflow: auto;
							text-align: center;
							color: #000;
						}

						.chooseNum {
							width: 100%;
							height: 58upx;
							padding: 0 38upx;
							box-sizing: border-box;
							display: flex;
							justify-content: space-between;
							margin-bottom: 86upx;
							margin-top: 20upx;

							.checkItem {
								width: 100upx;
								height: 58upx;
								line-height: 58upx;
								text-align: center;
								border-radius: 10upx;
								color: #888888;
								font-size: 25upx;
								background: #F3F4F6;

								&.isCheck {
									background: #5CB7FF;
									color: #fff;
								}
							}
						}
					}

					.isChoose {
						display: none;
						background-color: #fff;
						overflow: hidden;
						border-radius: 0 0 15upx 15upx;
						padding-bottom: 41upx;
						margin-bottom: 58upx;

						.contactList {
							width: 100%;
							height: auto;
							margin-top: 20upx;

							.contactItem {
								width: 577upx;
								height: 158upx;
								box-sizing: border-box;
								padding: 0 29upx;
								display: flex;
								justify-content: space-between;
								margin: 0 auto;
								background: #F3F4F6;
								border-radius: 15upx;
								margin-bottom: 19upx;

								.left {
									width: auto;
									height: 100%;
									display: flex;
									flex-direction: column;
									justify-content: center;

									.peopleName {
										color: #000;
										font-size: 29upx;
										font-weight: 600;
										margin-bottom: 10upx;
									}

									.peopleCard,
									.peopleMablie {
										color: #888;
										font-size: 23upx;
									}

									.peopleMablie {
										text {
											&:first-child {
												display: inline-block;
												margin-right: 96upx;
											}
										}
									}
								}

								.right {
									width: 38upx;
									height: 100%;
									display: flex;
									align-items: center;

									.checkBtn {
										width: 38upx;
										height: 38upx;
										line-height: 38upx;
										text-align: center;
										border: 2upx solid #888888;
										border-radius: 38upx;
										font-weight: 700;

										&.isCheck {
											background: #FFBA38;
											color: #000;
											border: none;
											font-family: 'icongou';

											&::before {
												content: "\e66c";
												display: inline-block;
											}
										}
									}
								}

							}
						}
					}

					//已经选择了的话
					&.isChooseVenue {
						width: 654upx;
						height: auto;
						margin-bottom: 27upx;

						.show_fixed_box {
							width: 100%;
							height: 587upx;
							background: url(../../static/img/entervenue/choose_venue.png) no-repeat center center;
							background-size: 100% 100%;
						}

						.isChoose {
							display: block;

							.title {
								padding-top: 15upx;
							}
						}
					}
				}

			}

			.venceInfofont {
				width: 100%;
				height: 180upx;
				display: flex;
				align-items: center;
				justify-content: center;
				position: fixed;
				bottom: 0;

				.submitBtn {
					width: 615upx;
					height: 77upx;
					line-height: 77upx;
					text-align: center;
					background: #5CB7FF;
					box-shadow: 0upx 6upx 12upx rgba(82, 162, 225, 0.34);
					border-radius: 10upx;
					font-size: 35upx;
				}
			}

			.mask {
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.5);
				position: fixed;
				left: 0;
				top: 0;
				bottom: 0;
				overflow: auto;
			}
			.mask .maskContent {
				width: 654upx;
				height: auto;
				background: #FFFFFF;
				border: 2upx solid #707070;
				border-radius: 19upx;
				margin: 237upx auto 237upx auto;
				box-sizing: border-box;
				padding: 58upx 58upx 48upx 58upx;
				font-family: 'PingFang SC';
			}
			.mask .maskContent .noticeTitle {
				color: #1B1B1B;
				font-size: 35upx;
				font-weight: bold;
				margin-bottom: 60upx;
			}
			.mask .maskContent .noticeView {
				width: 100%;
				height: auto;
				text-align: justify;
			}
			.mask .maskContent .noticeView .noticeItem {
				width: 100%;
				height: auto;
				margin-bottom: 52upx;
			}
			.mask .maskContent .noticeView .noticeItem .itemTitle {
				color: #5D5D5D;
				font-size: 29upx;
				font-weight: 700;
				line-height: 50upx;
			}
			.mask .maskContent .noticeView .noticeItem .itemContent {
				color: #888888;
				font-size: 27upx;
				line-height: 50upx;
			}
			.mask .maskContent .agreeBtn {
				width: 381upx;
				height: 77upx;
				text-align: center;
				line-height: 77upx;
				background: #5CB7FF;
				box-shadow: 0upx 6upx 12upx rgba(82, 162, 225, 0.34);
				border-radius: 10upx;
				font-size: 35upx;
				color: #fff;
				margin: 0 auto;
			}
			.mask .maskContent .agreeBtn.gray {
				background-color: #888888;
				box-shadow: 0upx 6upx 12upx rgba(136, 136, 136, 0.34);
			}

		}
		// 个人预约按钮
		.reservation-header {
			background-color: #007AFF;
			color: #ffffff;
			font-size: 30rpx;
			//   font-weight: bold;
			text-align: center;
			padding: 16rpx 0;
			width: 90%;
			margin: 40rpx auto 30rpx auto;
			border-radius: 16rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		}
	}
</style>
