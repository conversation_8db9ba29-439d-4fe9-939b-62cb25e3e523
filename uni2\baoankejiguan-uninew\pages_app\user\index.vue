<template>
  <view class="user">
    <Header
      :isBack="true"
      :isShowHome="false"
      title="个人中心"
      :background="`transparent`"
    >
    </Header>
    <view class="user_box">
      <view class="user_info">
        <view class="user_info_avatar">
          <image :src="maskedAvatar" mode=""></image>
        </view>
        <view class="user_info_text">
          <view class="user_info_name">
            {{ userInfo.nickName }}
          </view>
          <view class="user_info_phone">
            <image src="../../static/img/user/phone.png" mode=""></image>
            <text>{{ maskedPhone }}</text>
          </view>
        </view>
        <view class="user_info_out" @click="outLoginInfo">退出登录</view>
      </view>
      <view class="contacts_btn" @click="tocontacts">
        <image
          class="contacts_image"
          src="../../static/img/user/contacts.png"
          mode=""
        ></image>
        <text>联系人管理</text>
        <image
          class="forward_icon"
          src="../../static/img/user/back.png"
          mode=""
        ></image>
      </view>

      <view class="my_order">
        <view class="my_order_title">常用功能</view>
        <view class="my_order_btns">
          <view
            class="my_btn"
            v-for="(item, index) in functionList"
            :key="index"
            @click="jumpTo(item.path)"
          >
            <image :src="item.icon" class="btn_icon_box"></image>
			   <text>{{ item.name }}</text>
            <view
              v-if="item.num && item.num != 0"
              class="badge"
              :class="String(item.num).length > 1 ? 'overflowOneLength' : ''"
              >{{ item.num }}</view
            >
          </view>
        </view>
      </view>

      <view class="my_order">
        <view class="my_order_title"> 我的预约 </view>
        <view class="my_order_btns">
          <view
            class="my_btn"
            v-for="(jump, index) in jumpList"
            :key="index"
            @click="jumpTo(jump.path)"
          >
            <view class="btn_icon_box">
              <image :src="jump.icon" mode=""></image>
              <view
                v-if="jump.num && jump.num !== 0"
                :class="[
                  'badge',
                  String(jump.num).length > 1 ? 'overflowOneLength' : '',
                ]"
              >
                {{ jump.num ? jump.num : "" }}
              </view>
            </view>
            <text>{{ jump.name }}</text>
          </view>
        </view>
      </view>
      <view class="not_use" style="display: none">
        <view class="not_use_title"> 待使用 </view>
        <view class="not_use_list">
          <view class="not_use_item">
            <view class="item_title"> 入馆预约 </view>
            <view class="item_info">
              <view class="order_day">
                预约时间：
                <text>2021-11-06</text>
              </view>
              <view class="order_time">
                入馆时间：
                <text>上午场09:00-12:00</text>
              </view>
              <view class="look_qrCode"> 查看凭证 </view>
            </view>
          </view>

          <view class="not_use_item">
            <view class="item_title"> 观影预约 </view>
            <view class="item_info">
              <view class="order_filmName">
                影片名称：
                <text>神秘宇宙2</text>
              </view>
              <view class="order_day">
                预约时间：
                <text>2021-11-06</text>
              </view>
              <view class="order_time">
                入馆时间：
                <text>上午场09:00-12:00</text>
              </view>
              <view class="look_qrCode"> 二维码检票 </view>
            </view>
          </view>

          <view class="not_use_item">
            <view class="item_title"> 课程预约 </view>
            <view class="item_info">
              <view class="order_day">
                预约时间：
                <text>2021-11-06</text>
              </view>
              <view class="order_time">
                入馆时间：
                <text>上午场09:00-12:00</text>
              </view>
              <view class="look_qrCode"> 查看凭证 </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Header from "@/component/header.vue";
import baseConfig from "@/utils/baseConfig.js";
import { checkLoginPromise } from "@/utils/auth.js";
export default {
  data() {
    return {
      iSzgm: baseConfig.iSzgm,
      loginReady: false,
      userInfo: {
        avatar: "",
        nickName: "",
        sex: null,
        phonenumber: null,
      },
      jumpList: [
        {
          bg: "linear-gradient(180deg, #54AEFF 0%, #2D93F9 100%)",
          icon: require("../../static/img/user/venue.png"),
          path: "/pages_app/user/venuescheme",
          name: "参观预约",
          key: "venue",
        },
        {
          bg: "linear-gradient(180deg, #FFCA5F 0%, #FFB33C 100%)",
          icon: require("../../static/img/user/film.png"),
          path: "/pages_app/user/filmscheme",
          name: "观影预约",
          key: "film",
        },
        {
          bg: "linear-gradient(180deg, #744AFF 0%, #5A29FF 100%)",
          icon: require("../../static/img/user/course.png"),
          path: "/pages_app/user/curriculumscheme",
          name: "课程预约",
          key: "course",
        },
      ],
      functionList: [
        {
          bg: "linear-gradient(180deg, #54AEFF 0%, #2D93F9 100%)",
          icon: require("../../static/img/user/app_getbalance.png"),
          path: "/pages_app/user/oneqrcode",
          name: "管理员码",
          key: "code",
        },
        {
          bg: "linear-gradient(180deg, #54AEFF 0%, #2D93F9 100%)",
          icon: require("../../static/img/user/app_getbalance.png"),
          path: "noticeSubcribe",
          name: "订阅通知",
          key: "notice",
        },
      ],
    };
  },
  components: {
    Header,
  },
  async onLoad() {
    try {
      // 如果没登录，引导微信登录
      // #ifdef MP-WEIXIN
      //需要定义 ：this.userInfo + this.loginReady
      await checkLoginPromise(this); // 👈 登录完成后再继续执行
      console.log("登录后的 userInfo:", this.userInfo);
      // #endif

      // #ifdef H5
      // 只在 H5 平台编译执行的代码
      console.log("当前是 H5 环境");
      // #endif
      
      //执行业务
      this.getBadge();
    } catch (e) {
      console.warn("登录失败或取消", e);
    }
  },
  onShow() {
    if (!this.loginReady) {
      console.log("登录未完成，跳过 onShow 逻辑");
      return;
    } else {
      this.getBadge();
    }
  },
  computed: {
    maskedPhone() {
      const phone = this.userInfo?.phonenumber || "";
      return phone.length >= 7
        ? phone.replace(phone.substring(3, 7), "****")
        : phone;
    },
    maskedAvatar() {
      return this.userInfo?.avatar || "/static/img/user/avatar.jpg";
    },
  },
  methods: {
    getBadge() {
      this.$myRequest({
        // url: "/auth/linkman/isSign",
        url: "/apitp/Userlinkman/isSign",
      }).then((res) => {
        if (res.data.code === 200) {
          let data = res.data.data;
          this.jumpList.forEach((item) => {
            this.$set(item, "num", data[item.key].num);
          });
        }
      });
    },
    jumpTo(path) {
      uni.navigateTo({
        url: path,
      });
    },
    tocontacts() {
      uni.navigateTo({
        // url: "/pages_app/contactmanager/index",
        url: "/pages_app/contacts/index",
      });
    },
    outLoginInfo() {
      const that = this;
      const token = uni.getStorageSync("token");
      uni.showModal({
        content: "是否确定退出登录?",
        success: function (res) {
          if (res.confirm) {
            that
              .$myRequest({
                url: `/wx/user/${that.$appID}/logout`,
                data: {
                  token: token,
                },
              })
              .then((res) => {
                if (res.data.code === 200) {
                  // uni.removeStorageSync("token");
                  uni.showToast({ title: "退出成功", duration: 4000 });
                  setTimeout(function () {
                    uni.redirectTo({ url: "/pages/index/index" });
                  }, 1000);
                  uni.removeStorageSync("userInfo");
                }
              });
          } else if (res.cancel) {
          }
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.user {
  width: 100%;
  height: auto;
  min-height: 100vh;
  background: url(../../static/img/user/user.png) no-repeat center top #f3f4f6;
  background-size: 100% auto;
  font-family: "PingFang SC";

  .user_box {
    box-sizing: border-box;
    padding: 20upx 28upx 0;
    height: 100%;

    .user_info {
      display: flex;
      align-items: center;
      padding-left: 26upx;
      color: #fff;
      position: relative;

      .user_info_avatar {
        width: 120upx;
        height: 120upx;

        image {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          border: 5upx solid #fff;
        }
      }

      .user_info_text {
        margin-left: 30upx;
        height: 120upx;
        display: flex;
        flex-direction: column;
        justify-content: space-around;

        .user_info_name {
          font-size: 15px;
          font-weight: 600;
        }

        .user_info_phone {
          width: 220upx;
          height: 44upx;
          background-color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 38upx;

          image {
            width: 12upx;
            height: 20upx;
          }

          text {
            margin-left: 10upx;
            font-size: 24upx;
            font-family: "PingFang SC";
            color: #888888;
          }
        }
      }

      .user_info_out {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 160rpx;
        height: 50rpx;
        background: #07c160;
        border-radius: 8rpx;
        line-height: 50rpx;
        text-align: center;
      }
    }

    .contacts_btn {
      width: 100%;
      height: 86upx;
      position: relative;
      display: flex;
      align-items: center;
      border-radius: 10upx;
      margin-top: 38upx;
      background-color: #fff;

      .contacts_image {
        width: 44upx;
        height: 44upx;
        margin-left: 28upx;
      }

      text {
        font-size: 27upx;
        font-family: "PingFang SC";
        font-weight: 400;
        color: #000000;
        margin-left: 26upx;
        font-weight: 600;
      }

      .forward_icon {
        width: 12upx;
        height: 24upx;
        position: absolute;
        right: 32upx;
      }
    }

    .my_order {
      width: 100%;
      height: 250upx;
      padding: 20upx;
      margin-top: 20upx;
      border-radius: 10upx;
      box-sizing: border-box;
      background-color: #fff;

      .my_order_title {
        font-size: 34upx;
        font-family: "PingFang SC";
        font-weight: 600;
        color: #000000;
      }

      .my_order_btns {
        height: 120upx;
        display: flex;
        justify-content: space-between;
        margin-top: 28upx;
        padding: 0 20upx;

        .my_btn {
          position: relative;

          .btn_icon_box {
            width: 76upx;
            height: 76upx;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 5upx auto;
            position: relative;

            image {
              width: 100%;
              height: 100%;
            }

            .badge {
              padding: 0 12upx;
              background: #d51d1d;
              border-radius: 50%;
              font-size: 27upx;
              position: absolute;
              top: -4upx;
              left: 58upx;
              color: #fff;
              box-sizing: border-box;

              &.overflowOneLength {
                border-radius: 19upx;
              }
            }
          }

          text {
            font-size: 26upx;
            font-family: "PingFang SC";
            font-weight: 600;
            color: #000000;
          }
        }
      }
    }

    .not_use {
      width: 100%;
      padding: 20upx;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 10upx;
      margin-top: 20upx;

      .not_use_title {
        font-size: 34upx;
        font-family: "PingFang SC";
        font-weight: 600;
        color: #000000;
      }

      .not_use_list {
        width: 100%;

        .not_use_item {
          width: 100%;
          padding: 20upx;
          box-sizing: border-box;
          background-color: #f3f4f6;
          border-radius: 10upx;
          margin-top: 20upx;

          .item_title {
            font-size: 28upx;
            font-family: "PingFang SC";
            font-weight: 600;
            color: #000000;
          }

          .item_info {
            position: relative;
            font-size: 28upx;
            font-family: "PingFang SC";
            font-weight: 400;
            color: #6f6f6f;

            & > view {
              margin-top: 10upx;

              text {
                color: #000;
              }
            }

            .look_qrCode {
              width: 154upx;
              height: 58upx;
              background: linear-gradient(180deg, #ffca5f 0%, #ffb33c 100%);
              box-shadow: 0px 6upx 12upx rgba(255, 186, 56, 0.34);
              border-radius: 19px;

              font-size: 26upx;
              font-family: PingFang SC;
              font-weight: 400;
              color: #ffffff;
              line-height: 58upx;
              text-align: center;
              position: absolute;
              top: 50%;
              right: 0;
              transform: translateY(-50%);
              margin-top: 0;
            }
          }
        }
      }
    }
  }
}
</style>
